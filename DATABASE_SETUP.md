# Database Setup Guide

## Quick Fix for Current Issue

The app is failing because the database tables don't exist. Here's how to fix it:

### Option 1: Manual Setup (Recommended)

1. **Open Supabase Dashboard**
   - Go to [https://supabase.com/dashboard](https://supabase.com/dashboard)
   - Select your project: `gsuvqpwagpdwwcmtggyy`

2. **Open SQL Editor**
   - Click on "SQL Editor" in the left sidebar
   - Click "New Query"

3. **Copy and paste this SQL:**

```sql
-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- User profiles table
CREATE TABLE IF NOT EXISTS public.user_profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT,
    credits INTEGER DEFAULT 0,
    daily_uses INTEGER DEFAULT 0,
    last_use_date DATE DEFAULT CURRENT_DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Photo analyses table
CREATE TABLE IF NOT EXISTS public.photo_analyses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    image_url TEXT,
    estimated_age INTEGER,
    confidence_score DECIMAL(5,4),
    analysis_result JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Anonymous uses table
CREATE TABLE IF NOT EXISTS public.anonymous_uses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    ip_address TEXT NOT NULL,
    use_date DATE DEFAULT CURRENT_DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(ip_address, use_date)
);

-- Credit transactions table
CREATE TABLE IF NOT EXISTS public.credit_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    amount DECIMAL(10,2) NOT NULL,
    credits_added INTEGER NOT NULL,
    stripe_payment_intent_id TEXT,
    status TEXT NOT NULL DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX IF NOT EXISTS idx_photo_analyses_user_id ON public.photo_analyses(user_id);
CREATE INDEX IF NOT EXISTS idx_photo_analyses_created_at ON public.photo_analyses(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_anonymous_uses_ip_date ON public.anonymous_uses(ip_address, use_date);

-- Enable RLS
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.photo_analyses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.credit_transactions ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view own profile" ON public.user_profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.user_profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.user_profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can view own analyses" ON public.photo_analyses
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own analyses" ON public.photo_analyses
    FOR INSERT WITH CHECK (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Users can view own transactions" ON public.credit_transactions
    FOR SELECT USING (auth.uid() = user_id);

-- Auto-create user profile trigger
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.user_profiles (id, email, credits, daily_uses, last_use_date)
    VALUES (NEW.id, NEW.email, 0, 0, CURRENT_DATE);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
```

4. **Run the Query**
   - Click "Run" button
   - You should see "Success. No rows returned" message

5. **Verify Tables**
   - Go to "Table Editor" in the left sidebar
   - You should see the 4 new tables: `user_profiles`, `photo_analyses`, `anonymous_uses`, `credit_transactions`

### Option 2: Using the Script

Run this command in your terminal:
```bash
npm run db:init
```

## After Setup

1. **Restart your development server:**
   ```bash
   # Stop the current server (Ctrl+C)
   npm run dev
   ```

2. **Test the application:**
   - Go to http://localhost:3001 (or whatever port it shows)
   - Try uploading a photo
   - Try creating an account

## Troubleshooting

If you still see errors:

1. **Check Supabase connection:**
   - Verify your `.env.local` file has correct Supabase URL and keys
   - Make sure your Supabase project is active

2. **Check table permissions:**
   - In Supabase dashboard, go to Authentication > Policies
   - Make sure RLS policies are enabled

3. **Check logs:**
   - Look at the terminal where `npm run dev` is running
   - Check browser console for errors

## What These Tables Do

- **user_profiles**: Stores user info, credits, and daily usage limits
- **photo_analyses**: Stores history of age analysis results
- **anonymous_uses**: Tracks daily usage for non-registered users
- **credit_transactions**: Records credit purchases and payments
