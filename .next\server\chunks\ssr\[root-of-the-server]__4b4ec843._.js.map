{"version": 3, "sources": [], "sections": [{"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/coding/guess-my-age/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createBrowserClient } from '@supabase/ssr'\n\n// Get environment variables with fallbacks\nconst getSupabaseUrl = () => process.env.NEXT_PUBLIC_SUPABASE_URL || ''\nconst getSupabaseAnonKey = () => process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ''\nconst getSupabaseServiceKey = () => process.env.SUPABASE_SERVICE_ROLE_KEY || ''\n\n// Client-side Supabase client (lazy initialization)\nexport const createSupabaseClient = () => {\n  const url = getSupabaseUrl()\n  const key = getSupabaseAnonKey()\n\n  if (!url || !key) {\n    throw new Error('Supabase URL and Anon Key are required')\n  }\n\n  return createClient(url, key)\n}\n\n// Browser client for client components\nexport const createSupabaseBrowserClient = () => {\n  const url = getSupabaseUrl()\n  const key = getSupabaseAnonKey()\n\n  if (!url || !key) {\n    throw new Error('Supabase URL and Anon Key are required')\n  }\n\n  return createBrowserClient(url, key)\n}\n\n// Service role client for admin operations (server-side only)\nexport const createSupabaseAdminClient = () => {\n  const url = getSupabaseUrl()\n  const serviceKey = getSupabaseServiceKey()\n\n  if (!url || !serviceKey) {\n    throw new Error('Supabase URL and Service Role Key are required')\n  }\n\n  return createClient(url, serviceKey, {\n    auth: {\n      autoRefreshToken: false,\n      persistSession: false\n    }\n  })\n}\n\n// Legacy exports for backward compatibility\nexport const getSupabaseClient = () => createSupabaseClient()\nexport const getSupabaseAdminClient = () => createSupabaseAdminClient()\n\n// Database types\nexport interface UserProfile {\n  id: string\n  email: string | null\n  credits: number\n  daily_uses: number\n  last_use_date: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface PhotoAnalysis {\n  id: string\n  user_id: string | null\n  image_url: string | null\n  estimated_age: number | null\n  confidence_score: number | null\n  analysis_result: Record<string, unknown>\n  created_at: string\n}\n\nexport interface CreditTransaction {\n  id: string\n  user_id: string\n  amount: number\n  credits_added: number\n  stripe_payment_intent_id: string | null\n  status: string\n  created_at: string\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AAAA;;;AAEA,2CAA2C;AAC3C,MAAM,iBAAiB,IAAM,gFAAwC;AACrE,MAAM,qBAAqB,IAAM,wPAA6C;AAC9E,MAAM,wBAAwB,IAAM,QAAQ,GAAG,CAAC,yBAAyB,IAAI;AAGtE,MAAM,uBAAuB;IAClC,MAAM,MAAM;IACZ,MAAM,MAAM;IAEZ,uCAAkB;;IAElB;IAEA,OAAO,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,KAAK;AAC3B;AAGO,MAAM,8BAA8B;IACzC,MAAM,MAAM;IACZ,MAAM,MAAM;IAEZ,uCAAkB;;IAElB;IAEA,OAAO,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK;AAClC;AAGO,MAAM,4BAA4B;IACvC,MAAM,MAAM;IACZ,MAAM,aAAa;IAEnB,IAAI,CAAC,OAAO,CAAC,YAAY;QACvB,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,KAAK,YAAY;QACnC,MAAM;YACJ,kBAAkB;YAClB,gBAAgB;QAClB;IACF;AACF;AAGO,MAAM,oBAAoB,IAAM;AAChC,MAAM,yBAAyB,IAAM", "debugId": null}}, {"offset": {"line": 156, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/coding/guess-my-age/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useEffect, useState, useCallback } from 'react'\nimport { User } from '@supabase/supabase-js'\nimport { createSupabaseClient } from '@/lib/supabase'\nimport type { UserProfile } from '@/lib/supabase'\n\ninterface AuthContextType {\n  user: User | null\n  profile: UserProfile | null\n  loading: boolean\n  signIn: (email: string, password: string) => Promise<{ error: Error | null }>\n  signUp: (email: string, password: string) => Promise<{ error: Error | null }>\n  signOut: () => Promise<void>\n  refreshProfile: () => Promise<void>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [profile, setProfile] = useState<UserProfile | null>(null)\n  const [loading, setLoading] = useState(false) // Start with false to avoid infinite loading\n  const [supabase] = useState(() => {\n    try {\n      return createSupabaseClient()\n    } catch (error) {\n      console.error('Failed to create Supabase client:', error)\n      // Return a mock client to prevent crashes\n      return null\n    }\n  })\n\n  const fetchProfile = useCallback(async (userId: string) => {\n    try {\n      if (!supabase) {\n        console.error('Supabase client not available for profile fetch')\n        setProfile(null)\n        return\n      }\n\n      const { data, error } = await supabase\n        .from('user_profiles')\n        .select('*')\n        .eq('id', userId)\n        .single()\n\n      if (error) {\n        // If profile doesn't exist, that's okay - user just doesn't have one yet\n        if (error.code === 'PGRST116') {\n          console.log('User profile not found, user is anonymous')\n          setProfile(null)\n          return\n        }\n        console.error('Error fetching profile:', error)\n        setProfile(null)\n        return\n      }\n\n      setProfile(data)\n    } catch (error) {\n      console.error('Error fetching profile:', error)\n      setProfile(null)\n    }\n  }, [supabase])\n\n  const refreshProfile = async () => {\n    if (user) {\n      await fetchProfile(user.id)\n    }\n  }\n\n  useEffect(() => {\n    const getSession = async () => {\n      try {\n        console.log('AuthContext: Getting session...')\n\n        if (!supabase) {\n          console.error('Supabase client not available')\n          setLoading(false)\n          return\n        }\n\n        const { data: { session } } = await supabase.auth.getSession()\n        console.log('AuthContext: Session received:', !!session?.user)\n        setUser(session?.user ?? null)\n\n        if (session?.user) {\n          console.log('AuthContext: Fetching profile for user:', session.user.id)\n          await fetchProfile(session.user.id)\n        }\n      } catch (error) {\n        console.error('Error getting session:', error)\n      } finally {\n        console.log('AuthContext: Setting loading to false')\n        setLoading(false)\n      }\n    }\n\n    // Set a timeout to ensure loading doesn't stay true forever\n    const timeoutId = setTimeout(() => {\n      console.warn('Auth loading timeout reached, setting loading to false')\n      setLoading(false)\n    }, 5000) // 5 seconds timeout\n\n    getSession()\n\n    if (!supabase) {\n      return () => clearTimeout(timeoutId)\n    }\n\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        clearTimeout(timeoutId) // Clear timeout since we got a response\n        setUser(session?.user ?? null)\n\n        if (session?.user) {\n          await fetchProfile(session.user.id)\n        } else {\n          setProfile(null)\n        }\n\n        setLoading(false)\n      }\n    )\n\n    return () => {\n      subscription.unsubscribe()\n      clearTimeout(timeoutId)\n    }\n  }, [fetchProfile, supabase.auth])\n\n  const signIn = async (email: string, password: string) => {\n    if (!supabase) {\n      return { error: new Error('Supabase client not available') }\n    }\n    const { error } = await supabase.auth.signInWithPassword({\n      email,\n      password,\n    })\n    return { error }\n  }\n\n  const signUp = async (email: string, password: string) => {\n    if (!supabase) {\n      return { error: new Error('Supabase client not available') }\n    }\n    const { error } = await supabase.auth.signUp({\n      email,\n      password,\n    })\n    return { error }\n  }\n\n  const signOut = async () => {\n    if (!supabase) {\n      return\n    }\n    await supabase.auth.signOut()\n  }\n\n  const value = {\n    user,\n    profile,\n    loading,\n    signIn,\n    signUp,\n    signOut,\n    refreshProfile,\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AAJA;;;;AAiBA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,6CAA6C;;IAC3F,MAAM,CAAC,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC1B,IAAI;YACF,OAAO,CAAA,GAAA,sHAAA,CAAA,uBAAoB,AAAD;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,0CAA0C;YAC1C,OAAO;QACT;IACF;IAEA,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACtC,IAAI;YACF,IAAI,CAAC,UAAU;gBACb,QAAQ,KAAK,CAAC;gBACd,WAAW;gBACX;YACF;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,MAAM;YAET,IAAI,OAAO;gBACT,yEAAyE;gBACzE,IAAI,MAAM,IAAI,KAAK,YAAY;oBAC7B,QAAQ,GAAG,CAAC;oBACZ,WAAW;oBACX;gBACF;gBACA,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,WAAW;gBACX;YACF;YAEA,WAAW;QACb,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,WAAW;QACb;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,iBAAiB;QACrB,IAAI,MAAM;YACR,MAAM,aAAa,KAAK,EAAE;QAC5B;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa;YACjB,IAAI;gBACF,QAAQ,GAAG,CAAC;gBAEZ,IAAI,CAAC,UAAU;oBACb,QAAQ,KAAK,CAAC;oBACd,WAAW;oBACX;gBACF;gBAEA,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;gBAC5D,QAAQ,GAAG,CAAC,kCAAkC,CAAC,CAAC,SAAS;gBACzD,QAAQ,SAAS,QAAQ;gBAEzB,IAAI,SAAS,MAAM;oBACjB,QAAQ,GAAG,CAAC,2CAA2C,QAAQ,IAAI,CAAC,EAAE;oBACtE,MAAM,aAAa,QAAQ,IAAI,CAAC,EAAE;gBACpC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;YAC1C,SAAU;gBACR,QAAQ,GAAG,CAAC;gBACZ,WAAW;YACb;QACF;QAEA,4DAA4D;QAC5D,MAAM,YAAY,WAAW;YAC3B,QAAQ,IAAI,CAAC;YACb,WAAW;QACb,GAAG,MAAM,oBAAoB;;QAE7B;QAEA,IAAI,CAAC,UAAU;YACb,OAAO,IAAM,aAAa;QAC5B;QAEA,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,SAAS,IAAI,CAAC,iBAAiB,CAChE,OAAO,OAAO;YACZ,aAAa,WAAW,wCAAwC;;YAChE,QAAQ,SAAS,QAAQ;YAEzB,IAAI,SAAS,MAAM;gBACjB,MAAM,aAAa,QAAQ,IAAI,CAAC,EAAE;YACpC,OAAO;gBACL,WAAW;YACb;YAEA,WAAW;QACb;QAGF,OAAO;YACL,aAAa,WAAW;YACxB,aAAa;QACf;IACF,GAAG;QAAC;QAAc,SAAS,IAAI;KAAC;IAEhC,MAAM,SAAS,OAAO,OAAe;QACnC,IAAI,CAAC,UAAU;YACb,OAAO;gBAAE,OAAO,IAAI,MAAM;YAAiC;QAC7D;QACA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;YACvD;YACA;QACF;QACA,OAAO;YAAE;QAAM;IACjB;IAEA,MAAM,SAAS,OAAO,OAAe;QACnC,IAAI,CAAC,UAAU;YACb,OAAO;gBAAE,OAAO,IAAI,MAAM;YAAiC;QAC7D;QACA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;YAC3C;YACA;QACF;QACA,OAAO;YAAE;QAAM;IACjB;IAEA,MAAM,UAAU;QACd,IAAI,CAAC,UAAU;YACb;QACF;QACA,MAAM,SAAS,IAAI,CAAC,OAAO;IAC7B;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}]}