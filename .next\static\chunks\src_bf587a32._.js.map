{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/coding/guess-my-age/src/lib/openai.ts"], "sourcesContent": ["// This file contains shared types and constants that can be used both client and server-side\n// Server-only OpenAI functions are in openai-server.ts\n\n\n\nexport interface AgeAnalysisResult {\n  estimatedAge: number | null\n  confidence: number\n  hasPersonDetected: boolean\n  explanation: string\n  error?: string\n}\n\nexport type DetailLevel = 'low' | 'medium' | 'high'\n\nexport interface DetailLevelConfig {\n  name: string\n  description: string\n  credits: number\n  model: string\n  imageDetail: 'low' | 'high'\n  maxTokens: number\n  promptComplexity: 'basic' | 'detailed' | 'comprehensive'\n}\n\nexport const DETAIL_LEVELS: Record<DetailLevel, DetailLevelConfig> = {\n  low: {\n    name: 'Basic Analysis',\n    description: 'Quick age estimation with basic explanation',\n    credits: 1,\n    model: 'gpt-4o-mini',\n    imageDetail: 'low',\n    maxTokens: 300,\n    promptComplexity: 'basic'\n  },\n  medium: {\n    name: 'Detailed Analysis',\n    description: 'Comprehensive age analysis with scientific explanation',\n    credits: 2,\n    model: 'gpt-4o',\n    imageDetail: 'high',\n    maxTokens: 500,\n    promptComplexity: 'detailed'\n  },\n  high: {\n    name: 'Expert Analysis',\n    description: 'In-depth professional analysis with multiple factors',\n    credits: 3,\n    model: 'gpt-4o',\n    imageDetail: 'high',\n    maxTokens: 800,\n    promptComplexity: 'comprehensive'\n  }\n}\n\n\n\n// Helper function to convert File to base64\nexport function fileToBase64(file: File): Promise<string> {\n  return new Promise((resolve, reject) => {\n    const reader = new FileReader()\n    reader.readAsDataURL(file)\n    reader.onload = () => {\n      const result = reader.result as string\n      // Remove the data URL prefix to get just the base64 string\n      const base64 = result.split(',')[1]\n      resolve(base64)\n    }\n    reader.onerror = error => reject(error)\n  })\n}\n\n// Validate image file\nexport function validateImageFile(file: File): { valid: boolean; error?: string } {\n  const maxSize = 10 * 1024 * 1024 // 10MB\n  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']\n  \n  if (!allowedTypes.includes(file.type)) {\n    return { \n      valid: false, \n      error: 'Please upload a valid image file (JPEG, PNG, or WebP)' \n    }\n  }\n  \n  if (file.size > maxSize) {\n    return { \n      valid: false, \n      error: 'Image file is too large. Please upload an image smaller than 10MB' \n    }\n  }\n  \n  return { valid: true }\n}\n"], "names": [], "mappings": "AAAA,6FAA6F;AAC7F,uDAAuD;;;;;;AAwBhD,MAAM,gBAAwD;IACnE,KAAK;QACH,MAAM;QACN,aAAa;QACb,SAAS;QACT,OAAO;QACP,aAAa;QACb,WAAW;QACX,kBAAkB;IACpB;IACA,QAAQ;QACN,MAAM;QACN,aAAa;QACb,SAAS;QACT,OAAO;QACP,aAAa;QACb,WAAW;QACX,kBAAkB;IACpB;IACA,MAAM;QACJ,MAAM;QACN,aAAa;QACb,SAAS;QACT,OAAO;QACP,aAAa;QACb,WAAW;QACX,kBAAkB;IACpB;AACF;AAKO,SAAS,aAAa,IAAU;IACrC,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,MAAM,SAAS,IAAI;QACnB,OAAO,aAAa,CAAC;QACrB,OAAO,MAAM,GAAG;YACd,MAAM,SAAS,OAAO,MAAM;YAC5B,2DAA2D;YAC3D,MAAM,SAAS,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE;YACnC,QAAQ;QACV;QACA,OAAO,OAAO,GAAG,CAAA,QAAS,OAAO;IACnC;AACF;AAGO,SAAS,kBAAkB,IAAU;IAC1C,MAAM,UAAU,KAAK,OAAO,KAAK,OAAO;;IACxC,MAAM,eAAe;QAAC;QAAc;QAAa;QAAa;KAAa;IAE3E,IAAI,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;QACrC,OAAO;YACL,OAAO;YACP,OAAO;QACT;IACF;IAEA,IAAI,KAAK,IAAI,GAAG,SAAS;QACvB,OAAO;YACL,OAAO;YACP,OAAO;QACT;IACF;IAEA,OAAO;QAAE,OAAO;IAAK;AACvB", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/coding/guess-my-age/src/components/PhotoUpload.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef } from 'react'\nimport { Upload, Camera, AlertCircle, CheckCircle, User, Clock, Settings } from 'lucide-react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { DETAIL_LEVELS, DetailLevel } from '@/lib/openai'\n\ninterface AnalysisResult {\n  estimatedAge: number | null\n  confidence: number\n  hasPersonDetected: boolean\n  explanation: string\n  detailLevel?: DetailLevel\n  creditsUsed?: number\n}\n\ninterface UsageInfo {\n  remainingUses: number\n  isAnonymous: boolean\n  needsCredits: boolean\n  canUseLevel?: {\n    low: boolean\n    medium: boolean\n    high: boolean\n  }\n}\n\ninterface PhotoUploadProps {\n  onNeedAuth: () => void\n  onNeedCredits: () => void\n}\n\nexport default function PhotoUpload({ onNeedAuth, onNeedCredits }: PhotoUploadProps) {\n  const [selectedFile, setSelectedFile] = useState<File | null>(null)\n  const [preview, setPreview] = useState<string | null>(null)\n  const [loading, setLoading] = useState(false)\n  const [result, setResult] = useState<AnalysisResult | null>(null)\n  const [usage, setUsage] = useState<UsageInfo | null>(null)\n  const [error, setError] = useState('')\n  const [detailLevel, setDetailLevel] = useState<DetailLevel>('medium')\n  const fileInputRef = useRef<HTMLInputElement>(null)\n  const { } = useAuth()\n\n  const handleFileSelect = async (file: File) => {\n    setSelectedFile(file)\n    setResult(null)\n    setError('')\n\n    // Create preview\n    const reader = new FileReader()\n    reader.onload = (e) => {\n      setPreview(e.target?.result as string)\n    }\n    reader.readAsDataURL(file)\n\n    // Fetch usage info to determine available detail levels\n    try {\n      const response = await fetch('/api/usage')\n      if (response.ok) {\n        const data = await response.json()\n        setUsage(data.usage)\n\n        // Set default detail level based on availability\n        if (data.usage.isAnonymous) {\n          setDetailLevel('low')\n        } else if (!data.usage.canUseLevel?.medium) {\n          setDetailLevel('low')\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch usage info:', error)\n    }\n  }\n\n  const handleDrop = (e: React.DragEvent) => {\n    e.preventDefault()\n    const files = e.dataTransfer.files\n    if (files.length > 0) {\n      handleFileSelect(files[0])\n    }\n  }\n\n  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const files = e.target.files\n    if (files && files.length > 0) {\n      handleFileSelect(files[0])\n    }\n  }\n\n  const analyzePhoto = async () => {\n    if (!selectedFile) return\n\n    setLoading(true)\n    setError('')\n\n    try {\n      const formData = new FormData()\n      formData.append('image', selectedFile)\n      formData.append('detailLevel', detailLevel)\n\n      const response = await fetch('/api/analyze', {\n        method: 'POST',\n        body: formData,\n      })\n\n      const data = await response.json()\n\n      if (!response.ok) {\n        if (response.status === 429) {\n          if (data.needsCredits) {\n            onNeedCredits()\n          } else if (data.isAnonymous) {\n            onNeedAuth()\n          }\n        }\n        throw new Error(data.error || 'Analysis failed')\n      }\n\n      setResult(data.analysis)\n      setUsage(data.usage)\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An error occurred')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const reset = () => {\n    setSelectedFile(null)\n    setPreview(null)\n    setResult(null)\n    setError('')\n    if (fileInputRef.current) {\n      fileInputRef.current.value = ''\n    }\n  }\n\n  return (\n    <div className=\"max-w-2xl mx-auto space-y-6\">\n      {/* Upload Area */}\n      <div\n        onDrop={handleDrop}\n        onDragOver={(e) => e.preventDefault()}\n        className={`border-2 border-dashed rounded-2xl p-8 text-center transition-colors ${\n          preview \n            ? 'border-blue-300 bg-blue-50' \n            : 'border-gray-300 hover:border-blue-400 hover:bg-gray-50'\n        }`}\n      >\n        {preview ? (\n          <div className=\"space-y-4\">\n            {/* eslint-disable-next-line @next/next/no-img-element */}\n            <img\n              src={preview}\n              alt=\"Preview\"\n              className=\"max-w-full max-h-64 mx-auto rounded-lg shadow-md\"\n            />\n\n            {/* Detail Level Selector */}\n            <div className=\"bg-gray-50 rounded-lg p-4 space-y-3\">\n              <div className=\"flex items-center space-x-2 mb-2\">\n                <Settings className=\"w-4 h-4 text-gray-600\" />\n                <h4 className=\"font-medium text-gray-900\">Analysis Detail Level</h4>\n              </div>\n              <div className=\"grid grid-cols-1 gap-2\">\n                {(Object.entries(DETAIL_LEVELS) as [DetailLevel, typeof DETAIL_LEVELS[DetailLevel]][]).map(([level, config]) => {\n                  const isAvailable = usage?.canUseLevel?.[level] !== false\n                  const isSelected = detailLevel === level\n\n                  return (\n                    <button\n                      key={level}\n                      onClick={() => setDetailLevel(level)}\n                      disabled={!isAvailable || loading}\n                      className={`p-3 rounded-lg border-2 text-left transition-all ${\n                        isSelected\n                          ? 'border-blue-500 bg-blue-50'\n                          : isAvailable\n                          ? 'border-gray-200 bg-white hover:border-gray-300'\n                          : 'border-gray-100 bg-gray-50 opacity-50 cursor-not-allowed'\n                      }`}\n                    >\n                      <div className=\"flex items-center justify-between\">\n                        <div className=\"flex-1\">\n                          <div className=\"flex items-center space-x-2\">\n                            <span className={`font-medium ${\n                              isSelected ? 'text-blue-900' : isAvailable ? 'text-gray-900' : 'text-gray-500'\n                            }`}>\n                              {config.name}\n                            </span>\n                            <span className={`text-xs px-2 py-1 rounded-full ${\n                              isSelected\n                                ? 'bg-blue-100 text-blue-700'\n                                : isAvailable\n                                ? 'bg-gray-100 text-gray-600'\n                                : 'bg-gray-50 text-gray-400'\n                            }`}>\n                              {config.credits} credit{config.credits > 1 ? 's' : ''}\n                            </span>\n                          </div>\n                          <p className={`text-sm mt-1 ${\n                            isSelected ? 'text-blue-700' : isAvailable ? 'text-gray-600' : 'text-gray-400'\n                          }`}>\n                            {config.description}\n                          </p>\n                        </div>\n                        {isSelected && (\n                          <CheckCircle className=\"w-5 h-5 text-blue-500 flex-shrink-0\" />\n                        )}\n                      </div>\n                    </button>\n                  )\n                })}\n              </div>\n              {usage?.isAnonymous && (\n                <p className=\"text-xs text-amber-600 bg-amber-50 p-2 rounded\">\n                  <AlertCircle className=\"w-3 h-3 inline mr-1\" />\n                  Anonymous users can only use Basic Analysis. Sign up for more options!\n                </p>\n              )}\n            </div>\n\n            <div className=\"flex justify-center space-x-3\">\n              <button\n                onClick={analyzePhoto}\n                disabled={loading}\n                className=\"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2\"\n              >\n                {loading ? (\n                  <>\n                    <div className=\"animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent\" />\n                    <span>Analyzing...</span>\n                  </>\n                ) : (\n                  <>\n                    <Camera className=\"w-4 h-4\" />\n                    <span>Analyze Age</span>\n                  </>\n                )}\n              </button>\n              <button\n                onClick={reset}\n                className=\"bg-gray-200 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-300 transition-colors\"\n              >\n                Choose Different Photo\n              </button>\n            </div>\n          </div>\n        ) : (\n          <div className=\"space-y-4\">\n            <div className=\"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto\">\n              <Upload className=\"w-8 h-8 text-blue-600\" />\n            </div>\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                Upload a Photo\n              </h3>\n              <p className=\"text-gray-600 mb-4\">\n                Drag and drop your photo here, or click to select\n              </p>\n              <button\n                onClick={() => fileInputRef.current?.click()}\n                className=\"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n              >\n                Select Photo\n              </button>\n            </div>\n            <input\n              ref={fileInputRef}\n              type=\"file\"\n              accept=\"image/*\"\n              onChange={handleFileInput}\n              className=\"hidden\"\n            />\n          </div>\n        )}\n      </div>\n\n      {/* Error Message */}\n      {error && (\n        <div className=\"bg-red-50 border border-red-200 rounded-lg p-4 flex items-start space-x-3\">\n          <AlertCircle className=\"w-5 h-5 text-red-500 flex-shrink-0 mt-0.5\" />\n          <div>\n            <h4 className=\"font-medium text-red-900\">Error</h4>\n            <p className=\"text-red-700 text-sm\">{error}</p>\n          </div>\n        </div>\n      )}\n\n      {/* Results */}\n      {result && (\n        <div className=\"bg-white border border-gray-200 rounded-2xl p-6 shadow-sm\">\n          <div className=\"flex items-center justify-between mb-4\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-10 h-10 bg-green-100 rounded-full flex items-center justify-center\">\n                  <CheckCircle className=\"w-5 h-5 text-green-600\" />\n                </div>\n                <h3 className=\"text-lg font-semibold text-gray-900\">Analysis Complete</h3>\n              </div>\n              {result.detailLevel && result.creditsUsed && (\n                <div className=\"text-right\">\n                  <div className=\"text-sm text-gray-600\">\n                    {DETAIL_LEVELS[result.detailLevel].name}\n                  </div>\n                  <div className=\"text-xs text-gray-500\">\n                    {result.creditsUsed} credit{result.creditsUsed > 1 ? 's' : ''} used\n                  </div>\n                </div>\n              )}\n            </div>\n\n          {result.hasPersonDetected ? (\n            <div className=\"space-y-4\">\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div className=\"bg-blue-50 rounded-lg p-4\">\n                  <div className=\"flex items-center space-x-2 mb-2\">\n                    <User className=\"w-4 h-4 text-blue-600\" />\n                    <span className=\"text-sm font-medium text-blue-900\">Estimated Age</span>\n                  </div>\n                  <div className=\"text-2xl font-bold text-blue-900\">\n                    {result.estimatedAge} years\n                  </div>\n                </div>\n                <div className=\"bg-green-50 rounded-lg p-4\">\n                  <div className=\"flex items-center space-x-2 mb-2\">\n                    <Clock className=\"w-4 h-4 text-green-600\" />\n                    <span className=\"text-sm font-medium text-green-900\">Confidence</span>\n                  </div>\n                  <div className=\"text-2xl font-bold text-green-900\">\n                    {Math.round(result.confidence * 100)}%\n                  </div>\n                </div>\n              </div>\n              <div className=\"bg-gray-50 rounded-lg p-4\">\n                <h4 className=\"font-medium text-gray-900 mb-3 flex items-center\">\n                  <span className=\"w-2 h-2 bg-blue-500 rounded-full mr-2\"></span>\n                  Scientific Analysis\n                </h4>\n                <div className=\"text-gray-700 text-sm leading-relaxed space-y-2\">\n                  {result.explanation.split('**').map((part, index) => {\n                    if (index % 2 === 1) {\n                      // This is a bold section (between **)\n                      return (\n                        <span key={index} className=\"font-semibold text-gray-900\">\n                          {part}\n                        </span>\n                      )\n                    } else {\n                      // This is regular text\n                      return part.split('\\n').map((line, lineIndex) => (\n                        <span key={`${index}-${lineIndex}`}>\n                          {line}\n                          {lineIndex < part.split('\\n').length - 1 && <br />}\n                        </span>\n                      ))\n                    }\n                  })}\n                </div>\n              </div>\n            </div>\n          ) : (\n            <div className=\"text-center py-4\">\n              <div className=\"w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3\">\n                <AlertCircle className=\"w-6 h-6 text-yellow-600\" />\n              </div>\n              <h4 className=\"font-medium text-gray-900 mb-2\">No Person Detected</h4>\n              <p className=\"text-gray-600 text-sm\">{result.explanation}</p>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Usage Info */}\n      {usage && (\n        <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h4 className=\"font-medium text-blue-900\">\n                {usage.isAnonymous ? 'Daily Limit' : 'Remaining Uses'}\n              </h4>\n              <p className=\"text-sm text-blue-700\">\n                {usage.isAnonymous \n                  ? `${usage.remainingUses} free analysis remaining today`\n                  : `${usage.remainingUses} analyses remaining`\n                }\n              </p>\n            </div>\n            {usage.needsCredits && (\n              <button\n                onClick={onNeedCredits}\n                className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm\"\n              >\n                Buy Credits\n              </button>\n            )}\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AALA;;;;;AAgCe,SAAS,YAAY,EAAE,UAAU,EAAE,aAAa,EAAoB;;IACjF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB;IAC5D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IACrD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC5D,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC9C,MAAM,EAAG,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAElB,MAAM,mBAAmB,OAAO;QAC9B,gBAAgB;QAChB,UAAU;QACV,SAAS;QAET,iBAAiB;QACjB,MAAM,SAAS,IAAI;QACnB,OAAO,MAAM,GAAG,CAAC;YACf,WAAW,EAAE,MAAM,EAAE;QACvB;QACA,OAAO,aAAa,CAAC;QAErB,wDAAwD;QACxD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,SAAS,KAAK,KAAK;gBAEnB,iDAAiD;gBACjD,IAAI,KAAK,KAAK,CAAC,WAAW,EAAE;oBAC1B,eAAe;gBACjB,OAAO,IAAI,CAAC,KAAK,KAAK,CAAC,WAAW,EAAE,QAAQ;oBAC1C,eAAe;gBACjB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,MAAM,QAAQ,EAAE,YAAY,CAAC,KAAK;QAClC,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,iBAAiB,KAAK,CAAC,EAAE;QAC3B;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,IAAI,SAAS,MAAM,MAAM,GAAG,GAAG;YAC7B,iBAAiB,KAAK,CAAC,EAAE;QAC3B;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,cAAc;QAEnB,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,SAAS;YACzB,SAAS,MAAM,CAAC,eAAe;YAE/B,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,MAAM;YACR;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,IAAI,SAAS,MAAM,KAAK,KAAK;oBAC3B,IAAI,KAAK,YAAY,EAAE;wBACrB;oBACF,OAAO,IAAI,KAAK,WAAW,EAAE;wBAC3B;oBACF;gBACF;gBACA,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,UAAU,KAAK,QAAQ;YACvB,SAAS,KAAK,KAAK;QACrB,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,QAAQ;QACZ,gBAAgB;QAChB,WAAW;QACX,UAAU;QACV,SAAS;QACT,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,KAAK,GAAG;QAC/B;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBACC,QAAQ;gBACR,YAAY,CAAC,IAAM,EAAE,cAAc;gBACnC,WAAW,CAAC,qEAAqE,EAC/E,UACI,+BACA,0DACJ;0BAED,wBACC,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BACC,KAAK;4BACL,KAAI;4BACJ,WAAU;;;;;;sCAIZ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC;4CAAG,WAAU;sDAA4B;;;;;;;;;;;;8CAE5C,6LAAC;oCAAI,WAAU;8CACZ,AAAC,OAAO,OAAO,CAAC,uHAAA,CAAA,gBAAa,EAAyD,GAAG,CAAC,CAAC,CAAC,OAAO,OAAO;wCACzG,MAAM,cAAc,OAAO,aAAa,CAAC,MAAM,KAAK;wCACpD,MAAM,aAAa,gBAAgB;wCAEnC,qBACE,6LAAC;4CAEC,SAAS,IAAM,eAAe;4CAC9B,UAAU,CAAC,eAAe;4CAC1B,WAAW,CAAC,iDAAiD,EAC3D,aACI,+BACA,cACA,mDACA,4DACJ;sDAEF,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAW,CAAC,YAAY,EAC5B,aAAa,kBAAkB,cAAc,kBAAkB,iBAC/D;kFACC,OAAO,IAAI;;;;;;kFAEd,6LAAC;wEAAK,WAAW,CAAC,+BAA+B,EAC/C,aACI,8BACA,cACA,8BACA,4BACJ;;4EACC,OAAO,OAAO;4EAAC;4EAAQ,OAAO,OAAO,GAAG,IAAI,MAAM;;;;;;;;;;;;;0EAGvD,6LAAC;gEAAE,WAAW,CAAC,aAAa,EAC1B,aAAa,kBAAkB,cAAc,kBAAkB,iBAC/D;0EACC,OAAO,WAAW;;;;;;;;;;;;oDAGtB,4BACC,6LAAC,8NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;;2CApCtB;;;;;oCAyCX;;;;;;gCAED,OAAO,6BACN,6LAAC;oCAAE,WAAU;;sDACX,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;wCAAwB;;;;;;;;;;;;;sCAMrD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,wBACC;;0DACE,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;0DAAK;;;;;;;qEAGR;;0DACE,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;0DAAK;;;;;;;;;;;;;8CAIZ,6LAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;yCAML,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;sCAEpB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAGzD,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,6LAAC;oCACC,SAAS,IAAM,aAAa,OAAO,EAAE;oCACrC,WAAU;8CACX;;;;;;;;;;;;sCAIH,6LAAC;4BACC,KAAK;4BACL,MAAK;4BACL,QAAO;4BACP,UAAU;4BACV,WAAU;;;;;;;;;;;;;;;;;YAOjB,uBACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAA2B;;;;;;0CACzC,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;YAM1C,wBACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,8NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;kDAEzB,6LAAC;wCAAG,WAAU;kDAAsC;;;;;;;;;;;;4BAErD,OAAO,WAAW,IAAI,OAAO,WAAW,kBACvC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ,uHAAA,CAAA,gBAAa,CAAC,OAAO,WAAW,CAAC,CAAC,IAAI;;;;;;kDAEzC,6LAAC;wCAAI,WAAU;;4CACZ,OAAO,WAAW;4CAAC;4CAAQ,OAAO,WAAW,GAAG,IAAI,MAAM;4CAAG;;;;;;;;;;;;;;;;;;;oBAMvE,OAAO,iBAAiB,iBACvB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;wDAAK,WAAU;kEAAoC;;;;;;;;;;;;0DAEtD,6LAAC;gDAAI,WAAU;;oDACZ,OAAO,YAAY;oDAAC;;;;;;;;;;;;;kDAGzB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC;wDAAK,WAAU;kEAAqC;;;;;;;;;;;;0DAEvD,6LAAC;gDAAI,WAAU;;oDACZ,KAAK,KAAK,CAAC,OAAO,UAAU,GAAG;oDAAK;;;;;;;;;;;;;;;;;;;0CAI3C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAK,WAAU;;;;;;4CAA+C;;;;;;;kDAGjE,6LAAC;wCAAI,WAAU;kDACZ,OAAO,WAAW,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM;4CACzC,IAAI,QAAQ,MAAM,GAAG;gDACnB,sCAAsC;gDACtC,qBACE,6LAAC;oDAAiB,WAAU;8DACzB;mDADQ;;;;;4CAIf,OAAO;gDACL,uBAAuB;gDACvB,OAAO,KAAK,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,0BACjC,6LAAC;;4DACE;4DACA,YAAY,KAAK,KAAK,CAAC,MAAM,MAAM,GAAG,mBAAK,6LAAC;;;;;;uDAFpC,GAAG,MAAM,CAAC,EAAE,WAAW;;;;;4CAKtC;wCACF;;;;;;;;;;;;;;;;;6CAKN,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;0CAEzB,6LAAC;gCAAG,WAAU;0CAAiC;;;;;;0CAC/C,6LAAC;gCAAE,WAAU;0CAAyB,OAAO,WAAW;;;;;;;;;;;;;;;;;;YAO/D,uBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CACX,MAAM,WAAW,GAAG,gBAAgB;;;;;;8CAEvC,6LAAC;oCAAE,WAAU;8CACV,MAAM,WAAW,GACd,GAAG,MAAM,aAAa,CAAC,8BAA8B,CAAC,GACtD,GAAG,MAAM,aAAa,CAAC,mBAAmB,CAAC;;;;;;;;;;;;wBAIlD,MAAM,YAAY,kBACjB,6LAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AASf;GAhXwB;;QASV,kIAAA,CAAA,UAAO;;;KATG", "debugId": null}}, {"offset": {"line": 890, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/coding/guess-my-age/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: string | Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(new Date(date))\n}\n\nexport function getClientIP(request: Request): string {\n  const forwarded = request.headers.get('x-forwarded-for')\n  const realIP = request.headers.get('x-real-ip')\n  const cfConnectingIP = request.headers.get('cf-connecting-ip')\n  \n  if (cfConnectingIP) return cfConnectingIP\n  if (realIP) return realIP\n  if (forwarded) return forwarded.split(',')[0].trim()\n  \n  return '127.0.0.1' // fallback for development\n}\n\nexport function formatCredits(credits: number): string {\n  return credits === 1 ? '1 credit' : `${credits} credits`\n}\n\nexport function formatPrice(cents: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(cents / 100)\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,YAAY,OAAgB;IAC1C,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC;IACtC,MAAM,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC;IACnC,MAAM,iBAAiB,QAAQ,OAAO,CAAC,GAAG,CAAC;IAE3C,IAAI,gBAAgB,OAAO;IAC3B,IAAI,QAAQ,OAAO;IACnB,IAAI,WAAW,OAAO,UAAU,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI;IAElD,OAAO,YAAY,2BAA2B;;AAChD;AAEO,SAAS,cAAc,OAAe;IAC3C,OAAO,YAAY,IAAI,aAAa,GAAG,QAAQ,QAAQ,CAAC;AAC1D;AAEO,SAAS,YAAY,KAAa;IACvC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC,QAAQ;AACpB", "debugId": null}}, {"offset": {"line": 941, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/coding/guess-my-age/src/components/AnalysisHistory.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useCallback } from 'react'\nimport { History, User, Clock, AlertCircle, ChevronLeft, ChevronRight } from 'lucide-react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { formatDate } from '@/lib/utils'\nimport type { PhotoAnalysis } from '@/lib/supabase'\n\ninterface HistoryResponse {\n  analyses: PhotoAnalysis[]\n  pagination: {\n    page: number\n    limit: number\n    total: number\n    totalPages: number\n  }\n}\n\nexport default function AnalysisHistory() {\n  const [history, setHistory] = useState<PhotoAnalysis[]>([])\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState('')\n  const [currentPage, setCurrentPage] = useState(1)\n  const [totalPages, setTotalPages] = useState(1)\n  const { user } = useAuth()\n\n  const fetchHistory = useCallback(async (page: number = 1) => {\n    if (!user) return\n\n    setLoading(true)\n    setError('')\n\n    try {\n      const response = await fetch(`/api/history?page=${page}&limit=10`)\n\n      if (!response.ok) {\n        throw new Error('Failed to fetch history')\n      }\n\n      const data: HistoryResponse = await response.json()\n      setHistory(data.analyses)\n      setCurrentPage(data.pagination.page)\n      setTotalPages(data.pagination.totalPages)\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An error occurred')\n    } finally {\n      setLoading(false)\n    }\n  }, [user])\n\n  useEffect(() => {\n    fetchHistory()\n  }, [user, fetchHistory])\n\n  const handlePageChange = (page: number) => {\n    fetchHistory(page)\n  }\n\n  if (!user) {\n    return (\n      <div className=\"text-center py-12\">\n        <div className=\"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n          <History className=\"w-8 h-8 text-gray-400\" />\n        </div>\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Sign In to View History</h3>\n        <p className=\"text-gray-600\">Create an account to keep track of your age analyses.</p>\n      </div>\n    )\n  }\n\n  if (loading) {\n    return (\n      <div className=\"text-center py-12\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-2 border-blue-600 border-t-transparent mx-auto mb-4\" />\n        <p className=\"text-gray-600\">Loading your history...</p>\n      </div>\n    )\n  }\n\n  if (error) {\n    return (\n      <div className=\"text-center py-12\">\n        <div className=\"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n          <AlertCircle className=\"w-8 h-8 text-red-500\" />\n        </div>\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Error Loading History</h3>\n        <p className=\"text-gray-600 mb-4\">{error}</p>\n        <button\n          onClick={() => fetchHistory(currentPage)}\n          className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n        >\n          Try Again\n        </button>\n      </div>\n    )\n  }\n\n  if (history.length === 0) {\n    return (\n      <div className=\"text-center py-12\">\n        <div className=\"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n          <History className=\"w-8 h-8 text-gray-400\" />\n        </div>\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">No Analysis History</h3>\n        <p className=\"text-gray-600\">Your age analysis history will appear here after you upload photos.</p>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex items-center space-x-3\">\n        <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\">\n          <History className=\"w-4 h-4 text-blue-600\" />\n        </div>\n        <h2 className=\"text-xl font-semibold text-gray-900\">Analysis History</h2>\n      </div>\n\n      <div className=\"space-y-4\">\n        {history.map((analysis) => (\n          <div key={analysis.id} className=\"bg-white border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow\">\n            <div className=\"flex items-start justify-between\">\n              <div className=\"flex-1\">\n                <div className=\"flex items-center space-x-3 mb-2\">\n                  {analysis.estimated_age ? (\n                    <div className=\"flex items-center space-x-2\">\n                      <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\">\n                        <User className=\"w-4 h-4 text-blue-600\" />\n                      </div>\n                      <div>\n                        <span className=\"font-semibold text-gray-900\">\n                          {analysis.estimated_age} years old\n                        </span>\n                        <span className=\"text-sm text-gray-500 ml-2\">\n                          ({Math.round((analysis.confidence_score || 0) * 100)}% confidence)\n                        </span>\n                      </div>\n                    </div>\n                  ) : (\n                    <div className=\"flex items-center space-x-2\">\n                      <div className=\"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center\">\n                        <AlertCircle className=\"w-4 h-4 text-yellow-600\" />\n                      </div>\n                      <span className=\"font-medium text-gray-900\">No person detected</span>\n                    </div>\n                  )}\n                </div>\n                \n                {(analysis.analysis_result as { explanation?: string })?.explanation && (\n                  <div className=\"text-sm text-gray-600 mb-2 leading-relaxed\">\n                    {(analysis.analysis_result as { explanation: string }).explanation.split('**').map((part, index) => {\n                      if (index % 2 === 1) {\n                        // This is a bold section (between **)\n                        return (\n                          <span key={index} className=\"font-semibold text-gray-800\">\n                            {part}\n                          </span>\n                        )\n                      } else {\n                        // This is regular text\n                        return part.split('\\n').map((line, lineIndex) => (\n                          <span key={`${index}-${lineIndex}`}>\n                            {line}\n                            {lineIndex < part.split('\\n').length - 1 && <br />}\n                          </span>\n                        ))\n                      }\n                    })}\n                  </div>\n                )}\n                \n                <div className=\"flex items-center space-x-2 text-xs text-gray-500\">\n                  <Clock className=\"w-3 h-3\" />\n                  <span>{formatDate(analysis.created_at)}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Pagination */}\n      {totalPages > 1 && (\n        <div className=\"flex items-center justify-between\">\n          <button\n            onClick={() => handlePageChange(currentPage - 1)}\n            disabled={currentPage <= 1}\n            className=\"flex items-center space-x-2 px-4 py-2 text-sm text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n          >\n            <ChevronLeft className=\"w-4 h-4\" />\n            <span>Previous</span>\n          </button>\n          \n          <span className=\"text-sm text-gray-600\">\n            Page {currentPage} of {totalPages}\n          </span>\n          \n          <button\n            onClick={() => handlePageChange(currentPage + 1)}\n            disabled={currentPage >= totalPages}\n            className=\"flex items-center space-x-2 px-4 py-2 text-sm text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n          >\n            <span>Next</span>\n            <ChevronRight className=\"w-4 h-4\" />\n          </button>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AALA;;;;;AAkBe,SAAS;;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IAC1D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEvB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,OAAO,OAAe,CAAC;YACtD,IAAI,CAAC,MAAM;YAEX,WAAW;YACX,SAAS;YAET,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,CAAC,kBAAkB,EAAE,KAAK,SAAS,CAAC;gBAEjE,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,OAAwB,MAAM,SAAS,IAAI;gBACjD,WAAW,KAAK,QAAQ;gBACxB,eAAe,KAAK,UAAU,CAAC,IAAI;gBACnC,cAAc,KAAK,UAAU,CAAC,UAAU;YAC1C,EAAE,OAAO,KAAK;gBACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAChD,SAAU;gBACR,WAAW;YACb;QACF;oDAAG;QAAC;KAAK;IAET,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR;QACF;oCAAG;QAAC;QAAM;KAAa;IAEvB,MAAM,mBAAmB,CAAC;QACxB,aAAa;IACf;IAEA,IAAI,CAAC,MAAM;QACT,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,2MAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;;;;;;8BAErB,6LAAC;oBAAG,WAAU;8BAA2C;;;;;;8BACzD,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;IAGnC;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;IAGnC;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;;;;;;8BAEzB,6LAAC;oBAAG,WAAU;8BAA2C;;;;;;8BACzD,6LAAC;oBAAE,WAAU;8BAAsB;;;;;;8BACnC,6LAAC;oBACC,SAAS,IAAM,aAAa;oBAC5B,WAAU;8BACX;;;;;;;;;;;;IAKP;IAEA,IAAI,QAAQ,MAAM,KAAK,GAAG;QACxB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,2MAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;;;;;;8BAErB,6LAAC;oBAAG,WAAU;8BAA2C;;;;;;8BACzD,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;IAGnC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,2MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;;;;;;kCAErB,6LAAC;wBAAG,WAAU;kCAAsC;;;;;;;;;;;;0BAGtD,6LAAC;gBAAI,WAAU;0BACZ,QAAQ,GAAG,CAAC,CAAC,yBACZ,6LAAC;wBAAsB,WAAU;kCAC/B,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ,SAAS,aAAa,iBACrB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;;gEACb,SAAS,aAAa;gEAAC;;;;;;;sEAE1B,6LAAC;4DAAK,WAAU;;gEAA6B;gEACzC,KAAK,KAAK,CAAC,CAAC,SAAS,gBAAgB,IAAI,CAAC,IAAI;gEAAK;;;;;;;;;;;;;;;;;;iEAK3D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,uNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;8DAEzB,6LAAC;oDAAK,WAAU;8DAA4B;;;;;;;;;;;;;;;;;oCAKhD,SAAS,eAAe,EAA+B,6BACvD,6LAAC;wCAAI,WAAU;kDACZ,AAAC,SAAS,eAAe,CAA6B,WAAW,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM;4CACxF,IAAI,QAAQ,MAAM,GAAG;gDACnB,sCAAsC;gDACtC,qBACE,6LAAC;oDAAiB,WAAU;8DACzB;mDADQ;;;;;4CAIf,OAAO;gDACL,uBAAuB;gDACvB,OAAO,KAAK,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,0BACjC,6LAAC;;4DACE;4DACA,YAAY,KAAK,KAAK,CAAC,MAAM,MAAM,GAAG,mBAAK,6LAAC;;;;;;uDAFpC,GAAG,MAAM,CAAC,EAAE,WAAW;;;;;4CAKtC;wCACF;;;;;;kDAIJ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;0DAAM,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,SAAS,UAAU;;;;;;;;;;;;;;;;;;;;;;;uBArDnC,SAAS,EAAE;;;;;;;;;;YA8DxB,aAAa,mBACZ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS,IAAM,iBAAiB,cAAc;wBAC9C,UAAU,eAAe;wBACzB,WAAU;;0CAEV,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,6LAAC;0CAAK;;;;;;;;;;;;kCAGR,6LAAC;wBAAK,WAAU;;4BAAwB;4BAChC;4BAAY;4BAAK;;;;;;;kCAGzB,6LAAC;wBACC,SAAS,IAAM,iBAAiB,cAAc;wBAC9C,UAAU,eAAe;wBACzB,WAAU;;0CAEV,6LAAC;0CAAK;;;;;;0CACN,6LAAC,yNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMpC;GA/LwB;;QAML,kIAAA,CAAA,UAAO;;;KANF", "debugId": null}}, {"offset": {"line": 1471, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/coding/guess-my-age/src/components/UserProfile.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { User, CreditCard, LogOut, Calendar } from 'lucide-react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { formatCredits } from '@/lib/utils'\n\ninterface UserProfileProps {\n  onBuyCredits: () => void\n}\n\nexport default function UserProfile({ onBuyCredits }: UserProfileProps) {\n  const { user, profile, signOut } = useAuth()\n  const [showDropdown, setShowDropdown] = useState(false)\n\n  if (!user || !profile) return null\n\n  const remainingDailyUses = Math.max(0, 3 - profile.daily_uses)\n  const isNewDay = profile.last_use_date !== new Date().toISOString().split('T')[0]\n  const actualRemainingUses = isNewDay ? 3 : remainingDailyUses\n\n  return (\n    <div className=\"relative\">\n      <button\n        onClick={() => setShowDropdown(!showDropdown)}\n        className=\"flex items-center space-x-2 bg-white border border-gray-200 rounded-lg px-3 py-2 hover:bg-gray-50 transition-colors\"\n      >\n        <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\">\n          <User className=\"w-4 h-4 text-blue-600\" />\n        </div>\n        <div className=\"text-left\">\n          <div className=\"text-sm font-medium text-gray-900\">\n            {user.email?.split('@')[0]}\n          </div>\n          <div className=\"text-xs text-gray-500\">\n            {formatCredits(profile.credits)}\n          </div>\n        </div>\n      </button>\n\n      {showDropdown && (\n        <>\n          <div \n            className=\"fixed inset-0 z-10\" \n            onClick={() => setShowDropdown(false)}\n          />\n          <div className=\"absolute right-0 mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-20\">\n            <div className=\"p-4 border-b border-gray-100\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center\">\n                  <User className=\"w-5 h-5 text-blue-600\" />\n                </div>\n                <div>\n                  <div className=\"font-medium text-gray-900\">{user.email}</div>\n                  <div className=\"text-sm text-gray-500\">\n                    Member since {new Date(profile.created_at).toLocaleDateString()}\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"p-4 space-y-4\">\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div className=\"bg-blue-50 rounded-lg p-3\">\n                  <div className=\"flex items-center space-x-2\">\n                    <Calendar className=\"w-4 h-4 text-blue-600\" />\n                    <span className=\"text-sm font-medium text-blue-900\">Daily Uses</span>\n                  </div>\n                  <div className=\"text-lg font-bold text-blue-900 mt-1\">\n                    {actualRemainingUses}/3\n                  </div>\n                  <div className=\"text-xs text-blue-700\">\n                    {isNewDay ? 'Refreshed today' : 'Remaining today'}\n                  </div>\n                </div>\n\n                <div className=\"bg-green-50 rounded-lg p-3\">\n                  <div className=\"flex items-center space-x-2\">\n                    <CreditCard className=\"w-4 h-4 text-green-600\" />\n                    <span className=\"text-sm font-medium text-green-900\">Credits</span>\n                  </div>\n                  <div className=\"text-lg font-bold text-green-900 mt-1\">\n                    {profile.credits}\n                  </div>\n                  <div className=\"text-xs text-green-700\">\n                    Extra analyses\n                  </div>\n                </div>\n              </div>\n\n              <button\n                onClick={() => {\n                  onBuyCredits()\n                  setShowDropdown(false)\n                }}\n                className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2\"\n              >\n                <CreditCard className=\"w-4 h-4\" />\n                <span>Buy More Credits</span>\n              </button>\n\n              <button\n                onClick={() => {\n                  signOut()\n                  setShowDropdown(false)\n                }}\n                className=\"w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-200 transition-colors flex items-center justify-center space-x-2\"\n              >\n                <LogOut className=\"w-4 h-4\" />\n                <span>Sign Out</span>\n              </button>\n            </div>\n          </div>\n        </>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AACA;;;AALA;;;;;AAWe,SAAS,YAAY,EAAE,YAAY,EAAoB;;IACpE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,IAAI,CAAC,QAAQ,CAAC,SAAS,OAAO;IAE9B,MAAM,qBAAqB,KAAK,GAAG,CAAC,GAAG,IAAI,QAAQ,UAAU;IAC7D,MAAM,WAAW,QAAQ,aAAa,KAAK,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IACjF,MAAM,sBAAsB,WAAW,IAAI;IAE3C,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,SAAS,IAAM,gBAAgB,CAAC;gBAChC,WAAU;;kCAEV,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAElB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACZ,KAAK,KAAK,EAAE,MAAM,IAAI,CAAC,EAAE;;;;;;0CAE5B,6LAAC;gCAAI,WAAU;0CACZ,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ,OAAO;;;;;;;;;;;;;;;;;;YAKnC,8BACC;;kCACE,6LAAC;wBACC,WAAU;wBACV,SAAS,IAAM,gBAAgB;;;;;;kCAEjC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DAA6B,KAAK,KAAK;;;;;;8DACtD,6LAAC;oDAAI,WAAU;;wDAAwB;wDACvB,IAAI,KAAK,QAAQ,UAAU,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;0CAMrE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,6LAAC;gEAAK,WAAU;0EAAoC;;;;;;;;;;;;kEAEtD,6LAAC;wDAAI,WAAU;;4DACZ;4DAAoB;;;;;;;kEAEvB,6LAAC;wDAAI,WAAU;kEACZ,WAAW,oBAAoB;;;;;;;;;;;;0DAIpC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;0EACtB,6LAAC;gEAAK,WAAU;0EAAqC;;;;;;;;;;;;kEAEvD,6LAAC;wDAAI,WAAU;kEACZ,QAAQ,OAAO;;;;;;kEAElB,6LAAC;wDAAI,WAAU;kEAAyB;;;;;;;;;;;;;;;;;;kDAM5C,6LAAC;wCACC,SAAS;4CACP;4CACA,gBAAgB;wCAClB;wCACA,WAAU;;0DAEV,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,6LAAC;0DAAK;;;;;;;;;;;;kDAGR,6LAAC;wCACC,SAAS;4CACP;4CACA,gBAAgB;wCAClB;wCACA,WAAU;;0DAEV,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtB;GA1GwB;;QACa,kIAAA,CAAA,UAAO;;;KADpB", "debugId": null}}, {"offset": {"line": 1824, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/coding/guess-my-age/src/components/AuthModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Dialog } from '@headlessui/react'\nimport { X, Mail, Lock } from 'lucide-react'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface AuthModalProps {\n  isOpen: boolean\n  onClose: () => void\n  initialMode?: 'signin' | 'signup'\n}\n\nexport default function AuthModal({ isOpen, onClose, initialMode = 'signin' }: AuthModalProps) {\n  const [mode, setMode] = useState<'signin' | 'signup'>(initialMode)\n  const [email, setEmail] = useState('')\n  const [password, setPassword] = useState('')\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n  \n  const { signIn, signUp } = useAuth()\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n    setError('')\n\n    try {\n      const { error } = mode === 'signin' \n        ? await signIn(email, password)\n        : await signUp(email, password)\n\n      if (error) {\n        setError(error.message)\n      } else {\n        if (mode === 'signup') {\n          setError('Check your email for the confirmation link!')\n        } else {\n          onClose()\n        }\n      }\n    } catch {\n      setError('An unexpected error occurred')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const resetForm = () => {\n    setEmail('')\n    setPassword('')\n    setError('')\n  }\n\n  const switchMode = () => {\n    setMode(mode === 'signin' ? 'signup' : 'signin')\n    resetForm()\n  }\n\n  return (\n    <Dialog open={isOpen} onClose={onClose} className=\"relative z-50\">\n      <div className=\"fixed inset-0 bg-black/30\" aria-hidden=\"true\" />\n      \n      <div className=\"fixed inset-0 flex items-center justify-center p-4\">\n        <Dialog.Panel className=\"mx-auto max-w-md w-full bg-white rounded-2xl shadow-xl\">\n          <div className=\"flex items-center justify-between p-6 border-b\">\n            <Dialog.Title className=\"text-xl font-semibold\">\n              {mode === 'signin' ? 'Sign In' : 'Create Account'}\n            </Dialog.Title>\n            <button\n              onClick={onClose}\n              className=\"text-gray-400 hover:text-gray-600 transition-colors\"\n            >\n              <X className=\"w-5 h-5\" />\n            </button>\n          </div>\n\n          <form onSubmit={handleSubmit} className=\"p-6 space-y-4\">\n            {error && (\n              <div className={`p-3 rounded-lg text-sm ${\n                error.includes('Check your email') \n                  ? 'bg-green-50 text-green-700 border border-green-200'\n                  : 'bg-red-50 text-red-700 border border-red-200'\n              }`}>\n                {error}\n              </div>\n            )}\n\n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Email\n              </label>\n              <div className=\"relative\">\n                <Mail className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\n                <input\n                  id=\"email\"\n                  type=\"email\"\n                  value={email}\n                  onChange={(e) => setEmail(e.target.value)}\n                  className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  placeholder=\"Enter your email\"\n                  required\n                />\n              </div>\n            </div>\n\n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Password\n              </label>\n              <div className=\"relative\">\n                <Lock className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\n                <input\n                  id=\"password\"\n                  type=\"password\"\n                  value={password}\n                  onChange={(e) => setPassword(e.target.value)}\n                  className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  placeholder=\"Enter your password\"\n                  required\n                  minLength={6}\n                />\n              </div>\n            </div>\n\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n            >\n              {loading ? 'Loading...' : (mode === 'signin' ? 'Sign In' : 'Create Account')}\n            </button>\n\n            <div className=\"text-center\">\n              <button\n                type=\"button\"\n                onClick={switchMode}\n                className=\"text-sm text-blue-600 hover:text-blue-700 transition-colors\"\n              >\n                {mode === 'signin' \n                  ? \"Don't have an account? Sign up\" \n                  : \"Already have an account? Sign in\"\n                }\n              </button>\n            </div>\n          </form>\n        </Dialog.Panel>\n      </div>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;;;AALA;;;;;AAae,SAAS,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,cAAc,QAAQ,EAAkB;;IAC3F,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IACtD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEjC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,SAAS,WACvB,MAAM,OAAO,OAAO,YACpB,MAAM,OAAO,OAAO;YAExB,IAAI,OAAO;gBACT,SAAS,MAAM,OAAO;YACxB,OAAO;gBACL,IAAI,SAAS,UAAU;oBACrB,SAAS;gBACX,OAAO;oBACL;gBACF;YACF;QACF,EAAE,OAAM;YACN,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,YAAY;QAChB,SAAS;QACT,YAAY;QACZ,SAAS;IACX;IAEA,MAAM,aAAa;QACjB,QAAQ,SAAS,WAAW,WAAW;QACvC;IACF;IAEA,qBACE,6LAAC,kLAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,SAAS;QAAS,WAAU;;0BAChD,6LAAC;gBAAI,WAAU;gBAA4B,eAAY;;;;;;0BAEvD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,kLAAA,CAAA,SAAM,CAAC,KAAK;oBAAC,WAAU;;sCACtB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,kLAAA,CAAA,SAAM,CAAC,KAAK;oCAAC,WAAU;8CACrB,SAAS,WAAW,YAAY;;;;;;8CAEnC,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAIjB,6LAAC;4BAAK,UAAU;4BAAc,WAAU;;gCACrC,uBACC,6LAAC;oCAAI,WAAW,CAAC,uBAAuB,EACtC,MAAM,QAAQ,CAAC,sBACX,uDACA,gDACJ;8CACC;;;;;;8CAIL,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA+C;;;;;;sDAGhF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oDACxC,WAAU;oDACV,aAAY;oDACZ,QAAQ;;;;;;;;;;;;;;;;;;8CAKd,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA+C;;;;;;sDAGnF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oDAC3C,WAAU;oDACV,aAAY;oDACZ,QAAQ;oDACR,WAAW;;;;;;;;;;;;;;;;;;8CAKjB,6LAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,UAAU,eAAgB,SAAS,WAAW,YAAY;;;;;;8CAG7D,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,MAAK;wCACL,SAAS;wCACT,WAAU;kDAET,SAAS,WACN,mCACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpB;GAzIwB;;QAOK,kIAAA,CAAA,UAAO;;;KAPZ", "debugId": null}}, {"offset": {"line": 2102, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/coding/guess-my-age/src/components/BuyCreditsModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Dialog } from '@headlessui/react'\nimport { X, CreditCard, Check } from 'lucide-react'\nimport { loadStripe } from '@stripe/stripe-js'\nimport { Elements, CardElement, useStripe, useElements } from '@stripe/react-stripe-js'\nimport { useAuth } from '@/contexts/AuthContext'\n\nconst stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!)\n\ninterface BuyCreditsModalProps {\n  isOpen: boolean\n  onClose: () => void\n}\n\nfunction CheckoutForm({ onSuccess, onError }: { onSuccess: () => void; onError: (error: string) => void }) {\n  const stripe = useStripe()\n  const elements = useElements()\n  const [loading, setLoading] = useState(false)\n  const { refreshProfile } = useAuth()\n\n  const handleSubmit = async (event: React.FormEvent) => {\n    event.preventDefault()\n    \n    if (!stripe || !elements) return\n    \n    setLoading(true)\n\n    try {\n      // Create payment intent\n      const response = await fetch('/api/create-payment-intent', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ packageType: 'basic' })\n      })\n\n      const { clientSecret, error } = await response.json()\n      \n      if (error) {\n        onError(error)\n        return\n      }\n\n      // Confirm payment\n      const { error: confirmError } = await stripe.confirmCardPayment(clientSecret, {\n        payment_method: {\n          card: elements.getElement(CardElement)!,\n        }\n      })\n\n      if (confirmError) {\n        onError(confirmError.message || 'Payment failed')\n      } else {\n        // Refresh user profile to show new credits\n        await refreshProfile()\n        onSuccess()\n      }\n    } catch {\n      onError('Payment processing failed')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <form onSubmit={handleSubmit} className=\"space-y-4\">\n      <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h3 className=\"font-semibold text-blue-900\">5 Credits Package</h3>\n            <p className=\"text-sm text-blue-700\">Perfect for occasional use</p>\n          </div>\n          <div className=\"text-right\">\n            <div className=\"text-2xl font-bold text-blue-900\">$5.00</div>\n            <div className=\"text-sm text-blue-700\">$1.00 per credit</div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"border border-gray-300 rounded-lg p-3\">\n        <CardElement\n          options={{\n            style: {\n              base: {\n                fontSize: '16px',\n                color: '#424770',\n                '::placeholder': {\n                  color: '#aab7c4',\n                },\n              },\n            },\n          }}\n        />\n      </div>\n\n      <button\n        type=\"submit\"\n        disabled={!stripe || loading}\n        className=\"w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2\"\n      >\n        <CreditCard className=\"w-4 h-4\" />\n        <span>{loading ? 'Processing...' : 'Buy 5 Credits for $5.00'}</span>\n      </button>\n    </form>\n  )\n}\n\nexport default function BuyCreditsModal({ isOpen, onClose }: BuyCreditsModalProps) {\n  const [success, setSuccess] = useState(false)\n  const [error, setError] = useState('')\n\n  const handleSuccess = () => {\n    setSuccess(true)\n    setError('')\n    setTimeout(() => {\n      setSuccess(false)\n      onClose()\n    }, 2000)\n  }\n\n  const handleError = (errorMessage: string) => {\n    setError(errorMessage)\n    setSuccess(false)\n  }\n\n  const handleClose = () => {\n    setSuccess(false)\n    setError('')\n    onClose()\n  }\n\n  return (\n    <Dialog open={isOpen} onClose={handleClose} className=\"relative z-50\">\n      <div className=\"fixed inset-0 bg-black/30\" aria-hidden=\"true\" />\n      \n      <div className=\"fixed inset-0 flex items-center justify-center p-4\">\n        <Dialog.Panel className=\"mx-auto max-w-md w-full bg-white rounded-2xl shadow-xl\">\n          <div className=\"flex items-center justify-between p-6 border-b\">\n            <Dialog.Title className=\"text-xl font-semibold\">\n              Buy Credits\n            </Dialog.Title>\n            <button\n              onClick={handleClose}\n              className=\"text-gray-400 hover:text-gray-600 transition-colors\"\n            >\n              <X className=\"w-5 h-5\" />\n            </button>\n          </div>\n\n          <div className=\"p-6\">\n            {success ? (\n              <div className=\"text-center py-8\">\n                <div className=\"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <Check className=\"w-8 h-8 text-green-600\" />\n                </div>\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                  Payment Successful!\n                </h3>\n                <p className=\"text-gray-600\">\n                  5 credits have been added to your account.\n                </p>\n              </div>\n            ) : (\n              <>\n                {error && (\n                  <div className=\"mb-4 p-3 bg-red-50 text-red-700 border border-red-200 rounded-lg text-sm\">\n                    {error}\n                  </div>\n                )}\n\n                <Elements stripe={stripePromise}>\n                  <CheckoutForm onSuccess={handleSuccess} onError={handleError} />\n                </Elements>\n\n                <div className=\"mt-4 text-xs text-gray-500 text-center\">\n                  Your payment is secured by Stripe. We don&apos;t store your card details.\n                </div>\n              </>\n            )}\n          </div>\n        </Dialog.Panel>\n      </div>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;AASiC;;AAPjC;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AACA;;;AAPA;;;;;;;AASA,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD;AAO/B,SAAS,aAAa,EAAE,SAAS,EAAE,OAAO,EAA+D;;IACvG,MAAM,SAAS,CAAA,GAAA,sLAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,sLAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEjC,MAAM,eAAe,OAAO;QAC1B,MAAM,cAAc;QAEpB,IAAI,CAAC,UAAU,CAAC,UAAU;QAE1B,WAAW;QAEX,IAAI;YACF,wBAAwB;YACxB,MAAM,WAAW,MAAM,MAAM,8BAA8B;gBACzD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE,aAAa;gBAAQ;YAC9C;YAEA,MAAM,EAAE,YAAY,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI;YAEnD,IAAI,OAAO;gBACT,QAAQ;gBACR;YACF;YAEA,kBAAkB;YAClB,MAAM,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,OAAO,kBAAkB,CAAC,cAAc;gBAC5E,gBAAgB;oBACd,MAAM,SAAS,UAAU,CAAC,sLAAA,CAAA,cAAW;gBACvC;YACF;YAEA,IAAI,cAAc;gBAChB,QAAQ,aAAa,OAAO,IAAI;YAClC,OAAO;gBACL,2CAA2C;gBAC3C,MAAM;gBACN;YACF;QACF,EAAE,OAAM;YACN,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC;QAAK,UAAU;QAAc,WAAU;;0BACtC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAC5C,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAEvC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAmC;;;;;;8CAClD,6LAAC;oCAAI,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;0BAK7C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,sLAAA,CAAA,cAAW;oBACV,SAAS;wBACP,OAAO;4BACL,MAAM;gCACJ,UAAU;gCACV,OAAO;gCACP,iBAAiB;oCACf,OAAO;gCACT;4BACF;wBACF;oBACF;;;;;;;;;;;0BAIJ,6LAAC;gBACC,MAAK;gBACL,UAAU,CAAC,UAAU;gBACrB,WAAU;;kCAEV,6LAAC,qNAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;kCACtB,6LAAC;kCAAM,UAAU,kBAAkB;;;;;;;;;;;;;;;;;;AAI3C;GA1FS;;QACQ,sLAAA,CAAA,YAAS;QACP,sLAAA,CAAA,cAAW;QAED,kIAAA,CAAA,UAAO;;;KAJ3B;AA4FM,SAAS,gBAAgB,EAAE,MAAM,EAAE,OAAO,EAAwB;;IAC/E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,gBAAgB;QACpB,WAAW;QACX,SAAS;QACT,WAAW;YACT,WAAW;YACX;QACF,GAAG;IACL;IAEA,MAAM,cAAc,CAAC;QACnB,SAAS;QACT,WAAW;IACb;IAEA,MAAM,cAAc;QAClB,WAAW;QACX,SAAS;QACT;IACF;IAEA,qBACE,6LAAC,kLAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,SAAS;QAAa,WAAU;;0BACpD,6LAAC;gBAAI,WAAU;gBAA4B,eAAY;;;;;;0BAEvD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,kLAAA,CAAA,SAAM,CAAC,KAAK;oBAAC,WAAU;;sCACtB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,kLAAA,CAAA,SAAM,CAAC,KAAK;oCAAC,WAAU;8CAAwB;;;;;;8CAGhD,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAIjB,6LAAC;4BAAI,WAAU;sCACZ,wBACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;kDAGzD,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;qDAK/B;;oCACG,uBACC,6LAAC;wCAAI,WAAU;kDACZ;;;;;;kDAIL,6LAAC,sLAAA,CAAA,WAAQ;wCAAC,QAAQ;kDAChB,cAAA,6LAAC;4CAAa,WAAW;4CAAe,SAAS;;;;;;;;;;;kDAGnD,6LAAC;wCAAI,WAAU;kDAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUxE;IA7EwB;MAAA", "debugId": null}}, {"offset": {"line": 2487, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/coding/guess-my-age/src/components/SupabaseTest.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { createSupabaseClient } from '@/lib/supabase'\n\nexport default function SupabaseTest() {\n  const [status, setStatus] = useState('Testing...')\n  const [error, setError] = useState<string | null>(null)\n\n  useEffect(() => {\n    const testConnection = async () => {\n      try {\n        console.log('Testing Supabase connection...')\n        const supabase = createSupabaseClient()\n        console.log('Supabase client created')\n        \n        // Test basic connection\n        const { data, error } = await supabase.from('user_profiles').select('count').limit(1)\n        \n        if (error) {\n          console.error('Supabase error:', error)\n          setError(error.message)\n          setStatus('Connection failed')\n        } else {\n          console.log('Supabase connection successful:', data)\n          setStatus('Connection successful')\n        }\n      } catch (err) {\n        console.error('Test error:', err)\n        setError(err instanceof Error ? err.message : 'Unknown error')\n        setStatus('Test failed')\n      }\n    }\n\n    testConnection()\n  }, [])\n\n  return (\n    <div className=\"p-4 bg-yellow-100 border border-yellow-400 rounded\">\n      <h3 className=\"font-bold\">Supabase Connection Test</h3>\n      <p>Status: {status}</p>\n      {error && <p className=\"text-red-600\">Error: {error}</p>}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;yDAAiB;oBACrB,IAAI;wBACF,QAAQ,GAAG,CAAC;wBACZ,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,uBAAoB,AAAD;wBACpC,QAAQ,GAAG,CAAC;wBAEZ,wBAAwB;wBACxB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,iBAAiB,MAAM,CAAC,SAAS,KAAK,CAAC;wBAEnF,IAAI,OAAO;4BACT,QAAQ,KAAK,CAAC,mBAAmB;4BACjC,SAAS,MAAM,OAAO;4BACtB,UAAU;wBACZ,OAAO;4BACL,QAAQ,GAAG,CAAC,mCAAmC;4BAC/C,UAAU;wBACZ;oBACF,EAAE,OAAO,KAAK;wBACZ,QAAQ,KAAK,CAAC,eAAe;wBAC7B,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;wBAC9C,UAAU;oBACZ;gBACF;;YAEA;QACF;iCAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAAY;;;;;;0BAC1B,6LAAC;;oBAAE;oBAAS;;;;;;;YACX,uBAAS,6LAAC;gBAAE,WAAU;;oBAAe;oBAAQ;;;;;;;;;;;;;AAGpD;GAvCwB;KAAA", "debugId": null}}, {"offset": {"line": 2582, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/coding/guess-my-age/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Camera, History, LogIn } from 'lucide-react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport PhotoUpload from '@/components/PhotoUpload'\nimport AnalysisHistory from '@/components/AnalysisHistory'\nimport UserProfile from '@/components/UserProfile'\nimport AuthModal from '@/components/AuthModal'\nimport BuyCreditsModal from '@/components/BuyCreditsModal'\nimport SupabaseTest from '@/components/SupabaseTest'\n\nexport default function Home() {\n  const [activeTab, setActiveTab] = useState<'upload' | 'history'>('upload')\n  const [showAuthModal, setShowAuthModal] = useState(false)\n  const [showCreditsModal, setShowCreditsModal] = useState(false)\n  const [authMode, setAuthMode] = useState<'signin' | 'signup'>('signin')\n  const { user, loading } = useAuth()\n\n  const handleNeedAuth = () => {\n    setAuthMode('signup')\n    setShowAuthModal(true)\n  }\n\n  const handleNeedCredits = () => {\n    if (!user) {\n      handleNeedAuth()\n    } else {\n      setShowCreditsModal(true)\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center flex-col space-y-4\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-2 border-blue-600 border-t-transparent\" />\n        <p>Loading authentication... (debug mode)</p>\n        <SupabaseTest />\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-16\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-10 h-10 bg-blue-600 rounded-xl flex items-center justify-center\">\n                <Camera className=\"w-6 h-6 text-white\" />\n              </div>\n              <div>\n                <h1 className=\"text-xl font-bold text-gray-900\">Guess My Age</h1>\n                <p className=\"text-sm text-gray-500\">AI-powered age estimation</p>\n              </div>\n            </div>\n\n            <div className=\"flex items-center space-x-4\">\n              {user ? (\n                <UserProfile onBuyCredits={() => setShowCreditsModal(true)} />\n              ) : (\n                <button\n                  onClick={() => {\n                    setAuthMode('signin')\n                    setShowAuthModal(true)\n                  }}\n                  className=\"flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n                >\n                  <LogIn className=\"w-4 h-4\" />\n                  <span>Sign In</span>\n                </button>\n              )}\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Navigation Tabs */}\n        <div className=\"flex space-x-1 bg-white rounded-lg p-1 mb-8 shadow-sm max-w-md mx-auto\">\n          <button\n            onClick={() => setActiveTab('upload')}\n            className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md transition-colors ${\n              activeTab === 'upload'\n                ? 'bg-blue-600 text-white'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            <Camera className=\"w-4 h-4\" />\n            <span>Upload Photo</span>\n          </button>\n          <button\n            onClick={() => setActiveTab('history')}\n            className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md transition-colors ${\n              activeTab === 'history'\n                ? 'bg-blue-600 text-white'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            <History className=\"w-4 h-4\" />\n            <span>History</span>\n          </button>\n        </div>\n\n        {/* Tab Content */}\n        <div className=\"max-w-4xl mx-auto\">\n          {activeTab === 'upload' ? (\n            <PhotoUpload\n              onNeedAuth={handleNeedAuth}\n              onNeedCredits={handleNeedCredits}\n            />\n          ) : (\n            <AnalysisHistory />\n          )}\n        </div>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-white border-t border-gray-200 mt-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"text-center\">\n            <p className=\"text-gray-600 text-sm\">\n              Powered by OpenAI GPT-4o-mini • Built with Next.js and Supabase\n            </p>\n            <p className=\"text-gray-500 text-xs mt-2\">\n              Your photos are analyzed securely and not stored on our servers\n            </p>\n          </div>\n        </div>\n      </footer>\n\n      {/* Modals */}\n      <AuthModal\n        isOpen={showAuthModal}\n        onClose={() => setShowAuthModal(false)}\n        initialMode={authMode}\n      />\n\n      <BuyCreditsModal\n        isOpen={showCreditsModal}\n        onClose={() => setShowCreditsModal(false)}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;AAYe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IACjE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IAC9D,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEhC,MAAM,iBAAiB;QACrB,YAAY;QACZ,iBAAiB;IACnB;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,MAAM;YACT;QACF,OAAO;YACL,oBAAoB;QACtB;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;8BAAE;;;;;;8BACH,6LAAC,qIAAA,CAAA,UAAY;;;;;;;;;;;IAGnB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAEpB,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAkC;;;;;;0DAChD,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAIzC,6LAAC;gCAAI,WAAU;0CACZ,qBACC,6LAAC,oIAAA,CAAA,UAAW;oCAAC,cAAc,IAAM,oBAAoB;;;;;yDAErD,6LAAC;oCACC,SAAS;wCACP,YAAY;wCACZ,iBAAiB;oCACnB;oCACA,WAAU;;sDAEV,6LAAC,2MAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASlB,6LAAC;gBAAK,WAAU;;kCAEd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,yFAAyF,EACnG,cAAc,WACV,2BACA,qCACJ;;kDAEF,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;kDAAK;;;;;;;;;;;;0CAER,6LAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,yFAAyF,EACnG,cAAc,YACV,2BACA,qCACJ;;kDAEF,6LAAC,2MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,6LAAC;kDAAK;;;;;;;;;;;;;;;;;;kCAKV,6LAAC;wBAAI,WAAU;kCACZ,cAAc,yBACb,6LAAC,oIAAA,CAAA,UAAW;4BACV,YAAY;4BACZ,eAAe;;;;;iDAGjB,6LAAC,wIAAA,CAAA,UAAe;;;;;;;;;;;;;;;;0BAMtB,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;0CAGrC,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;;;;;;;;;;;0BAQhD,6LAAC,kIAAA,CAAA,UAAS;gBACR,QAAQ;gBACR,SAAS,IAAM,iBAAiB;gBAChC,aAAa;;;;;;0BAGf,6LAAC,wIAAA,CAAA,UAAe;gBACd,QAAQ;gBACR,SAAS,IAAM,oBAAoB;;;;;;;;;;;;AAI3C;GAtIwB;;QAKI,kIAAA,CAAA,UAAO;;;KALX", "debugId": null}}]}