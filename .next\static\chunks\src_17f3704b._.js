(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/supabase.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "createSupabaseAdminClient": (()=>createSupabaseAdminClient),
    "createSupabaseBrowserClient": (()=>createSupabaseBrowserClient),
    "createSupabaseClient": (()=>createSupabaseClient),
    "getSupabaseAdminClient": (()=>getSupabaseAdminClient),
    "getSupabaseClient": (()=>getSupabaseClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/supabase-js/dist/module/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createBrowserClient$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/createBrowserClient.js [app-client] (ecmascript)");
;
;
// Get environment variables with fallbacks
const getSupabaseUrl = ()=>("TURBOPACK compile-time value", "https://gsuvqpwagpdwwcmtggyy.supabase.co") || '';
const getSupabaseAnonKey = ()=>("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdzdXZxcHdhZ3Bkd3djbXRnZ3l5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyMjUzNTcsImV4cCI6MjA2NzgwMTM1N30.32kuYPnA7apzAmEGTwGQVM-FxVSyobDvk-ii8jwWXUY") || '';
const getSupabaseServiceKey = ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.SUPABASE_SERVICE_ROLE_KEY || '';
const createSupabaseClient = ()=>{
    const url = getSupabaseUrl();
    const key = getSupabaseAnonKey();
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(url, key);
};
const createSupabaseBrowserClient = ()=>{
    const url = getSupabaseUrl();
    const key = getSupabaseAnonKey();
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createBrowserClient$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createBrowserClient"])(url, key);
};
const createSupabaseAdminClient = ()=>{
    const url = getSupabaseUrl();
    const serviceKey = getSupabaseServiceKey();
    if (!url || !serviceKey) {
        throw new Error('Supabase URL and Service Role Key are required');
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(url, serviceKey, {
        auth: {
            autoRefreshToken: false,
            persistSession: false
        }
    });
};
const getSupabaseClient = ()=>createSupabaseClient();
const getSupabaseAdminClient = ()=>createSupabaseAdminClient();
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthProvider": (()=>AuthProvider),
    "useAuth": (()=>useAuth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
const AuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function AuthProvider({ children }) {
    _s();
    const [user, setUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [profile, setProfile] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false) // Start with false to avoid infinite loading
    ;
    const [supabase] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        "AuthProvider.useState": ()=>{
            try {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createSupabaseClient"])();
            } catch (error) {
                console.error('Failed to create Supabase client:', error);
                // Return a mock client to prevent crashes
                return null;
            }
        }
    }["AuthProvider.useState"]);
    const fetchProfile = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "AuthProvider.useCallback[fetchProfile]": async (userId)=>{
            try {
                if (!supabase) {
                    console.error('Supabase client not available for profile fetch');
                    setProfile(null);
                    return;
                }
                const { data, error } = await supabase.from('user_profiles').select('*').eq('id', userId).single();
                if (error) {
                    // If profile doesn't exist, that's okay - user just doesn't have one yet
                    if (error.code === 'PGRST116') {
                        console.log('User profile not found, user is anonymous');
                        setProfile(null);
                        return;
                    }
                    console.error('Error fetching profile:', error);
                    setProfile(null);
                    return;
                }
                setProfile(data);
            } catch (error) {
                console.error('Error fetching profile:', error);
                setProfile(null);
            }
        }
    }["AuthProvider.useCallback[fetchProfile]"], [
        supabase
    ]);
    const refreshProfile = async ()=>{
        if (user) {
            await fetchProfile(user.id);
        }
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AuthProvider.useEffect": ()=>{
            const getSession = {
                "AuthProvider.useEffect.getSession": async ()=>{
                    try {
                        console.log('AuthContext: Getting session...');
                        if (!supabase) {
                            console.error('Supabase client not available');
                            setLoading(false);
                            return;
                        }
                        const { data: { session } } = await supabase.auth.getSession();
                        console.log('AuthContext: Session received:', !!session?.user);
                        setUser(session?.user ?? null);
                        if (session?.user) {
                            console.log('AuthContext: Fetching profile for user:', session.user.id);
                            await fetchProfile(session.user.id);
                        }
                    } catch (error) {
                        console.error('Error getting session:', error);
                    } finally{
                        console.log('AuthContext: Setting loading to false');
                        setLoading(false);
                    }
                }
            }["AuthProvider.useEffect.getSession"];
            // Set a timeout to ensure loading doesn't stay true forever
            const timeoutId = setTimeout({
                "AuthProvider.useEffect.timeoutId": ()=>{
                    console.warn('Auth loading timeout reached, setting loading to false');
                    setLoading(false);
                }
            }["AuthProvider.useEffect.timeoutId"], 5000) // 5 seconds timeout
            ;
            getSession();
            if (!supabase) {
                return ({
                    "AuthProvider.useEffect": ()=>clearTimeout(timeoutId)
                })["AuthProvider.useEffect"];
            }
            const { data: { subscription } } = supabase.auth.onAuthStateChange({
                "AuthProvider.useEffect": async (event, session)=>{
                    clearTimeout(timeoutId) // Clear timeout since we got a response
                    ;
                    setUser(session?.user ?? null);
                    if (session?.user) {
                        await fetchProfile(session.user.id);
                    } else {
                        setProfile(null);
                    }
                    setLoading(false);
                }
            }["AuthProvider.useEffect"]);
            return ({
                "AuthProvider.useEffect": ()=>{
                    subscription.unsubscribe();
                    clearTimeout(timeoutId);
                }
            })["AuthProvider.useEffect"];
        }
    }["AuthProvider.useEffect"], [
        fetchProfile,
        supabase.auth
    ]);
    const signIn = async (email, password)=>{
        if (!supabase) {
            return {
                error: new Error('Supabase client not available')
            };
        }
        const { error } = await supabase.auth.signInWithPassword({
            email,
            password
        });
        return {
            error
        };
    };
    const signUp = async (email, password)=>{
        if (!supabase) {
            return {
                error: new Error('Supabase client not available')
            };
        }
        const { error } = await supabase.auth.signUp({
            email,
            password
        });
        return {
            error
        };
    };
    const signOut = async ()=>{
        if (!supabase) {
            return;
        }
        await supabase.auth.signOut();
    };
    const value = {
        user,
        profile,
        loading,
        signIn,
        signUp,
        signOut,
        refreshProfile
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/AuthContext.tsx",
        lineNumber: 173,
        columnNumber: 5
    }, this);
}
_s(AuthProvider, "iLMOXZI3OQ22xXaers5QDJQFsKg=");
_c = AuthProvider;
function useAuth() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
}
_s1(useAuth, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
__turbopack_context__.k.register(_c, "AuthProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_17f3704b._.js.map