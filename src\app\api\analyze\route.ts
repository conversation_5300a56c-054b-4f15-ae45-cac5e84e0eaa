import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase-server'
import { analyzeImageAge } from '@/lib/openai-server'
import { validateImageFile, DetailLevel, DETAIL_LEVELS } from '@/lib/openai'
import { checkUsageLimit, consumeUse } from '@/lib/credits'
import { getClientIP } from '@/lib/utils'
import { initializeDatabaseIfNeeded, getDatabaseSetupInstructions } from '@/lib/database-init'

export async function POST(request: NextRequest) {
  try {
    // Check if database is initialized
    const dbReady = await initializeDatabaseIfNeeded()
    if (!dbReady) {
      return NextResponse.json(
        {
          error: 'Database setup required',
          setupInstructions: getDatabaseSetupInstructions()
        },
        { status: 503 }
      )
    }

    const supabase = await createSupabaseServerClient()

    // Get user if authenticated
    const { data: { user } } = await supabase.auth.getUser()
    const userId = user?.id
    const ipAddress = getClientIP(request)

    // Check usage limits
    const usageCheck = await checkUsageLimit(userId, ipAddress)
    
    if (!usageCheck.canUse) {
      return NextResponse.json(
        { 
          error: usageCheck.needsCredits 
            ? 'You have reached your daily limit. Purchase credits to continue.' 
            : 'Daily limit reached. Please try again tomorrow or create an account for more uses.',
          needsCredits: usageCheck.needsCredits,
          isAnonymous: usageCheck.isAnonymous
        },
        { status: 429 }
      )
    }

    // Parse form data
    const formData = await request.formData()
    const file = formData.get('image') as File
    const detailLevelParam = formData.get('detailLevel') as string

    if (!file) {
      return NextResponse.json({ error: 'No image file provided' }, { status: 400 })
    }

    // Validate detail level
    const detailLevel: DetailLevel = (detailLevelParam && ['low', 'medium', 'high'].includes(detailLevelParam))
      ? detailLevelParam as DetailLevel
      : 'medium'

    const levelConfig = DETAIL_LEVELS[detailLevel]

    // Check if user can use this detail level
    if (!usageCheck.canUseLevel?.[detailLevel]) {
      return NextResponse.json(
        {
          error: `Insufficient credits for ${levelConfig.name}. This analysis requires ${levelConfig.credits} credits.`,
          needsCredits: true,
          isAnonymous: usageCheck.isAnonymous,
          requiredCredits: levelConfig.credits
        },
        { status: 429 }
      )
    }

    // Validate image file
    const validation = validateImageFile(file)
    if (!validation.valid) {
      return NextResponse.json({ error: validation.error }, { status: 400 })
    }

    // Convert file to base64
    const arrayBuffer = await file.arrayBuffer()
    const base64 = Buffer.from(arrayBuffer).toString('base64')

    // Analyze image with OpenAI
    const analysis = await analyzeImageAge(base64, detailLevel)
    
    if (analysis.error) {
      return NextResponse.json({ error: analysis.error }, { status: 500 })
    }

    // Consume the use (daily limit or credit) based on detail level
    const consumed = await consumeUse(userId, ipAddress, levelConfig.credits)
    if (!consumed) {
      return NextResponse.json(
        { error: 'Failed to process request. Please try again.' },
        { status: 500 }
      )
    }

    // Store analysis result in database
    const analysisData = {
      user_id: userId,
      image_url: null, // We're not storing the actual image for privacy
      estimated_age: analysis.estimatedAge,
      confidence_score: analysis.confidence,
      analysis_result: {
        hasPersonDetected: analysis.hasPersonDetected,
        explanation: analysis.explanation,
        timestamp: new Date().toISOString()
      }
    }

    const { data: savedAnalysis, error: saveError } = await supabase
      .from('photo_analyses')
      .insert(analysisData)
      .select()
      .single()

    if (saveError) {
      console.error('Error saving analysis:', saveError)
      // Don't fail the request if we can't save to history
    }

    // Get updated usage info
    const updatedUsageCheck = await checkUsageLimit(userId, ipAddress)

    return NextResponse.json({
      success: true,
      analysis: {
        estimatedAge: analysis.estimatedAge,
        confidence: analysis.confidence,
        hasPersonDetected: analysis.hasPersonDetected,
        explanation: analysis.explanation,
        detailLevel: detailLevel,
        creditsUsed: levelConfig.credits
      },
      usage: {
        remainingUses: updatedUsageCheck.remainingUses,
        isAnonymous: updatedUsageCheck.isAnonymous,
        needsCredits: updatedUsageCheck.needsCredits,
        canUseLevel: updatedUsageCheck.canUseLevel
      },
      analysisId: savedAnalysis?.id
    })

  } catch (error) {
    console.error('Analysis API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
