# 🚀 Quick Fix for Login Issues

## Problem
The app is showing errors because the database tables don't exist yet.

## Solution (5 minutes)

### Step 1: Open Supabase Dashboard
1. Go to [https://supabase.com/dashboard](https://supabase.com/dashboard)
2. Select your project: `gsuvqpwagpdwwcmtggyy`

### Step 2: Run Database Setup
1. Click **"SQL Editor"** in the left sidebar
2. Click **"New Query"**
3. Copy and paste this SQL:

```sql
-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- User profiles table
CREATE TABLE IF NOT EXISTS public.user_profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT,
    credits INTEGER DEFAULT 0,
    daily_uses INTEGER DEFAULT 0,
    last_use_date DATE DEFAULT CURRENT_DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Photo analyses table
CREATE TABLE IF NOT EXISTS public.photo_analyses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    image_url TEXT,
    estimated_age INTEGER,
    confidence_score DECIMAL(5,4),
    analysis_result JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Anonymous uses table
CREATE TABLE IF NOT EXISTS public.anonymous_uses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    ip_address TEXT NOT NULL,
    use_date DATE DEFAULT CURRENT_DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(ip_address, use_date)
);

-- Credit transactions table
CREATE TABLE IF NOT EXISTS public.credit_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    amount DECIMAL(10,2) NOT NULL,
    credits_added INTEGER NOT NULL,
    stripe_payment_intent_id TEXT,
    status TEXT NOT NULL DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_photo_analyses_user_id ON public.photo_analyses(user_id);
CREATE INDEX IF NOT EXISTS idx_photo_analyses_created_at ON public.photo_analyses(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_anonymous_uses_ip_date ON public.anonymous_uses(ip_address, use_date);

-- Enable Row Level Security
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.photo_analyses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.credit_transactions ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view own profile" ON public.user_profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.user_profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.user_profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can view own analyses" ON public.photo_analyses
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own analyses" ON public.photo_analyses
    FOR INSERT WITH CHECK (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Users can view own transactions" ON public.credit_transactions
    FOR SELECT USING (auth.uid() = user_id);

-- Auto-create user profile when user signs up
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.user_profiles (id, email, credits, daily_uses, last_use_date)
    VALUES (NEW.id, NEW.email, 0, 0, CURRENT_DATE);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
```

4. Click **"Run"** button
5. You should see "Success. No rows returned"

### Step 3: Verify Setup
1. Go to **"Table Editor"** in the left sidebar
2. You should see 4 new tables:
   - `user_profiles`
   - `photo_analyses`
   - `anonymous_uses`
   - `credit_transactions`

### Step 4: Test the App
1. Go back to your app: [http://localhost:3000](http://localhost:3000)
2. Try uploading a photo
3. Try creating an account and logging in

## ✅ Done!
Your app should now work perfectly. The database is set up and ready to use.

## What This Fixed
- ❌ "relation does not exist" errors
- ❌ 429 errors on /api/analyze
- ❌ Login/signup issues
- ❌ History page errors

## Next Steps
- Test photo upload and age analysis
- Test user registration and login
- Test the credit system (when you add Stripe keys)

---

**Need help?** The app now shows helpful setup instructions automatically if the database isn't configured.
