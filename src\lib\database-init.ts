/**
 * Database initialization utilities
 * This module handles automatic database setup when tables don't exist
 */

import { createSupabaseAdminClient } from './supabase'

// SQL statements to create tables
const CREATE_TABLES_SQL = `
-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- User profiles table
CREATE TABLE IF NOT EXISTS public.user_profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT,
    credits INTEGER DEFAULT 0,
    daily_uses INTEGER DEFAULT 0,
    last_use_date DATE DEFAULT CURRENT_DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Photo analyses table
CREATE TABLE IF NOT EXISTS public.photo_analyses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    image_url TEXT,
    estimated_age INTEGER,
    confidence_score DECIMAL(5,4),
    analysis_result JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Anonymous uses table
CREATE TABLE IF NOT EXISTS public.anonymous_uses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    ip_address TEXT NOT NULL,
    use_date DATE DEFAULT CURRENT_DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(ip_address, use_date)
);

-- Credit transactions table
CREATE TABLE IF NOT EXISTS public.credit_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    amount DECIMAL(10,2) NOT NULL,
    credits_added INTEGER NOT NULL,
    stripe_payment_intent_id TEXT,
    status TEXT NOT NULL DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX IF NOT EXISTS idx_photo_analyses_user_id ON public.photo_analyses(user_id);
CREATE INDEX IF NOT EXISTS idx_photo_analyses_created_at ON public.photo_analyses(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_anonymous_uses_ip_date ON public.anonymous_uses(ip_address, use_date);

-- Enable RLS
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.photo_analyses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.credit_transactions ENABLE ROW LEVEL SECURITY;
`

const CREATE_POLICIES_SQL = `
-- RLS Policies for user_profiles
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'user_profiles' AND policyname = 'Users can view own profile') THEN
        CREATE POLICY "Users can view own profile" ON public.user_profiles
            FOR SELECT USING (auth.uid() = id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'user_profiles' AND policyname = 'Users can update own profile') THEN
        CREATE POLICY "Users can update own profile" ON public.user_profiles
            FOR UPDATE USING (auth.uid() = id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'user_profiles' AND policyname = 'Users can insert own profile') THEN
        CREATE POLICY "Users can insert own profile" ON public.user_profiles
            FOR INSERT WITH CHECK (auth.uid() = id);
    END IF;
END $$;

-- RLS Policies for photo_analyses
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'photo_analyses' AND policyname = 'Users can view own analyses') THEN
        CREATE POLICY "Users can view own analyses" ON public.photo_analyses
            FOR SELECT USING (auth.uid() = user_id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'photo_analyses' AND policyname = 'Users can insert own analyses') THEN
        CREATE POLICY "Users can insert own analyses" ON public.photo_analyses
            FOR INSERT WITH CHECK (auth.uid() = user_id OR user_id IS NULL);
    END IF;
END $$;

-- RLS Policies for credit_transactions
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'credit_transactions' AND policyname = 'Users can view own transactions') THEN
        CREATE POLICY "Users can view own transactions" ON public.credit_transactions
            FOR SELECT USING (auth.uid() = user_id);
    END IF;
END $$;
`

const CREATE_FUNCTIONS_SQL = `
-- Auto-create user profile function
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.user_profiles (id, email, credits, daily_uses, last_use_date)
    VALUES (NEW.id, NEW.email, 0, 0, CURRENT_DATE)
    ON CONFLICT (id) DO NOTHING;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'on_auth_user_created') THEN
        CREATE TRIGGER on_auth_user_created
            AFTER INSERT ON auth.users
            FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
    END IF;
END $$;
`

/**
 * Check if a table exists
 */
async function tableExists(tableName: string): Promise<boolean> {
  try {
    const supabase = createSupabaseAdminClient()
    const { error } = await supabase
      .from(tableName)
      .select('*')
      .limit(1)
    
    return !error || !error.message.includes('does not exist')
  } catch {
    return false
  }
}

/**
 * Initialize database tables if they don't exist
 */
export async function initializeDatabaseIfNeeded(): Promise<boolean> {
  try {
    console.log('🔍 Checking database tables...')
    
    const tables = ['user_profiles', 'photo_analyses', 'anonymous_uses', 'credit_transactions']
    const missingTables = []
    
    for (const table of tables) {
      const exists = await tableExists(table)
      if (!exists) {
        missingTables.push(table)
      }
    }
    
    if (missingTables.length === 0) {
      console.log('✅ All database tables exist')
      return true
    }
    
    console.log(`⚠️  Missing tables: ${missingTables.join(', ')}`)
    console.log('📋 Please run the database setup manually:')
    console.log('   1. Go to your Supabase dashboard')
    console.log('   2. Open SQL Editor')
    console.log('   3. Run the SQL from DATABASE_SETUP.md')
    console.log('   4. Restart your development server')
    
    return false
    
  } catch (error) {
    console.error('❌ Database check failed:', error)
    return false
  }
}

/**
 * Get database setup instructions
 */
export function getDatabaseSetupInstructions(): string {
  return `
🔧 DATABASE SETUP REQUIRED

Your database tables are missing. Please follow these steps:

1. Open Supabase Dashboard: https://supabase.com/dashboard
2. Select your project
3. Go to SQL Editor
4. Run the SQL from DATABASE_SETUP.md file
5. Restart your development server

This is a one-time setup that will create all necessary tables.
`
}
