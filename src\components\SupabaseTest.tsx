'use client'

import { useEffect, useState } from 'react'
import { createSupabaseClient } from '@/lib/supabase'

export default function SupabaseTest() {
  const [status, setStatus] = useState('Testing...')
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const testConnection = async () => {
      try {
        console.log('Testing Supabase connection...')
        const supabase = createSupabaseClient()
        console.log('Supabase client created')
        
        // Test basic connection
        const { data, error } = await supabase.from('user_profiles').select('count').limit(1)
        
        if (error) {
          console.error('Supabase error:', error)
          setError(error.message)
          setStatus('Connection failed')
        } else {
          console.log('Supabase connection successful:', data)
          setStatus('Connection successful')
        }
      } catch (err) {
        console.error('Test error:', err)
        setError(err instanceof Error ? err.message : 'Unknown error')
        setStatus('Test failed')
      }
    }

    testConnection()
  }, [])

  return (
    <div className="p-4 bg-yellow-100 border border-yellow-400 rounded">
      <h3 className="font-bold">Supabase Connection Test</h3>
      <p>Status: {status}</p>
      {error && <p className="text-red-600">Error: {error}</p>}
    </div>
  )
}
