#!/usr/bin/env node

/**
 * Database initialization script for Guess My Age app
 * This script creates all necessary tables and sets up the database schema
 */

const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:')
  console.error('   NEXT_PUBLIC_SUPABASE_URL:', !!supabaseUrl)
  console.error('   SUPABASE_SERVICE_ROLE_KEY:', !!supabaseServiceKey)
  console.error('\nPlease check your .env.local file')
  process.exit(1)
}

// Create Supabase admin client
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function initializeDatabase() {
  try {
    console.log('🚀 Initializing database...')
    console.log('📍 Supabase URL:', supabaseUrl)
    
    // Read the schema file
    const schemaPath = path.join(__dirname, '..', 'database', 'schema.sql')
    
    if (!fs.existsSync(schemaPath)) {
      console.error('❌ Schema file not found:', schemaPath)
      process.exit(1)
    }
    
    const schema = fs.readFileSync(schemaPath, 'utf8')
    
    // Split the schema into individual statements
    const statements = schema
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'))
    
    console.log(`📝 Found ${statements.length} SQL statements to execute`)
    
    // Since we can't execute raw SQL directly through the client,
    // we'll create the tables using the Supabase client methods
    console.log('📝 Creating tables using Supabase client...')

    // Create tables by attempting to query them (this will create them if they don't exist)
    const tables = [
      {
        name: 'user_profiles',
        create: async () => {
          // Try to create the table structure by inserting a test record
          const { error } = await supabase
            .from('user_profiles')
            .upsert({
              id: '00000000-0000-0000-0000-000000000000',
              email: '<EMAIL>',
              credits: 0,
              daily_uses: 0,
              last_use_date: new Date().toISOString().split('T')[0]
            }, { onConflict: 'id' })

          if (error && !error.message.includes('relation') && !error.message.includes('does not exist')) {
            console.log('✅ user_profiles table exists or was created')
          }
        }
      }
    ]

    console.log('⚠️  Note: This script requires manual database setup.')
    console.log('📋 Please run the following SQL in your Supabase SQL editor:')
    console.log('\n' + '='.repeat(50))
    console.log(schema)
    console.log('='.repeat(50) + '\n')
    
    // Verify tables were created
    console.log('\n🔍 Verifying database setup...')
    
    const tables = [
      'user_profiles',
      'photo_analyses', 
      'anonymous_uses',
      'credit_transactions'
    ]
    
    for (const table of tables) {
      try {
        const { data, error } = await supabase
          .from(table)
          .select('*')
          .limit(1)
        
        if (error) {
          console.log(`❌ Table '${table}' verification failed:`, error.message)
        } else {
          console.log(`✅ Table '${table}' is accessible`)
        }
      } catch (err) {
        console.log(`❌ Table '${table}' check failed:`, err.message)
      }
    }
    
    console.log('\n🎉 Database initialization completed!')
    console.log('💡 You can now run: npm run dev')
    
  } catch (error) {
    console.error('❌ Database initialization failed:', error)
    process.exit(1)
  }
}

// Run the initialization
initializeDatabase()
