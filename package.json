{"name": "guess-my-age", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:init": "node scripts/init-database.js"}, "dependencies": {"@headlessui/react": "^2.2.4", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.4.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.5", "clsx": "^2.1.1", "dotenv": "^17.2.0", "lucide-react": "^0.525.0", "next": "15.3.5", "openai": "^5.9.0", "react": "^19.0.0", "react-dom": "^19.0.0", "stripe": "^18.3.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "typescript": "^5"}}