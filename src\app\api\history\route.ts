import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase-server'
import { initializeDatabaseIfNeeded, getDatabaseSetupInstructions } from '@/lib/database-init'

export async function GET(request: NextRequest) {
  try {
    // Check if database is initialized
    const dbReady = await initializeDatabaseIfNeeded()
    if (!dbReady) {
      return NextResponse.json(
        {
          error: 'Database setup required',
          setupInstructions: getDatabaseSetupInstructions()
        },
        { status: 503 }
      )
    }

    const supabase = await createSupabaseServerClient()
    
    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const offset = (page - 1) * limit

    // Fetch user's photo analyses
    const { data: analyses, error: fetchError } = await supabase
      .from('photo_analyses')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (fetchError) {
      console.error('Error fetching analyses:', fetchError)
      return NextResponse.json({ error: 'Failed to fetch history' }, { status: 500 })
    }

    // Get total count for pagination
    const { count, error: countError } = await supabase
      .from('photo_analyses')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id)

    if (countError) {
      console.error('Error counting analyses:', countError)
    }

    return NextResponse.json({
      analyses: analyses || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    })

  } catch (error) {
    console.error('History API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
