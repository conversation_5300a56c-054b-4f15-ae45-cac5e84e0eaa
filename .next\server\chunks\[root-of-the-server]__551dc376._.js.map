{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/coding/guess-my-age/src/lib/supabase-server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Server client for server components and API routes\nexport const createSupabaseServerClient = async () => {\n  const cookieStore = await cookies()\n\n  return createServerClient(supabaseUrl, supabaseAnonKey, {\n    cookies: {\n      get(name: string) {\n        return cookieStore.get(name)?.value\n      },\n      set(name: string, value: string, options: Record<string, unknown>) {\n        cookieStore.set({ name, value, ...options })\n      },\n      remove(name: string, options: Record<string, unknown>) {\n        cookieStore.set({ name, value: '', ...options })\n      },\n    },\n  })\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEA,MAAM;AACN,MAAM;AAGC,MAAM,6BAA6B;IACxC,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,EAAE,aAAa,iBAAiB;QACtD,SAAS;YACP,KAAI,IAAY;gBACd,OAAO,YAAY,GAAG,CAAC,OAAO;YAChC;YACA,KAAI,IAAY,EAAE,KAAa,EAAE,OAAgC;gBAC/D,YAAY,GAAG,CAAC;oBAAE;oBAAM;oBAAO,GAAG,OAAO;gBAAC;YAC5C;YACA,QAAO,IAAY,EAAE,OAAgC;gBACnD,YAAY,GAAG,CAAC;oBAAE;oBAAM,OAAO;oBAAI,GAAG,OAAO;gBAAC;YAChD;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 188, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/coding/guess-my-age/src/app/api/history/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { createSupabaseServerClient } from '@/lib/supabase-server'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const supabase = await createSupabaseServerClient()\n    \n    // Get authenticated user\n    const { data: { user }, error: authError } = await supabase.auth.getUser()\n    \n    if (authError || !user) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    // Get query parameters\n    const { searchParams } = new URL(request.url)\n    const page = parseInt(searchParams.get('page') || '1')\n    const limit = parseInt(searchParams.get('limit') || '10')\n    const offset = (page - 1) * limit\n\n    // Fetch user's photo analyses\n    const { data: analyses, error: fetchError } = await supabase\n      .from('photo_analyses')\n      .select('*')\n      .eq('user_id', user.id)\n      .order('created_at', { ascending: false })\n      .range(offset, offset + limit - 1)\n\n    if (fetchError) {\n      console.error('Error fetching analyses:', fetchError)\n      return NextResponse.json({ error: 'Failed to fetch history' }, { status: 500 })\n    }\n\n    // Get total count for pagination\n    const { count, error: countError } = await supabase\n      .from('photo_analyses')\n      .select('*', { count: 'exact', head: true })\n      .eq('user_id', user.id)\n\n    if (countError) {\n      console.error('Error counting analyses:', countError)\n    }\n\n    return NextResponse.json({\n      analyses: analyses || [],\n      pagination: {\n        page,\n        limit,\n        total: count || 0,\n        totalPages: Math.ceil((count || 0) / limit)\n      }\n    })\n\n  } catch (error) {\n    console.error('History API error:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,kIAAA,CAAA,6BAA0B,AAAD;QAEhD,yBAAyB;QACzB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAExE,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,uBAAuB;QACvB,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;QAE5B,8BAA8B;QAC9B,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SACjD,IAAI,CAAC,kBACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM,GACvC,KAAK,CAAC,QAAQ,SAAS,QAAQ;QAElC,IAAI,YAAY;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA0B,GAAG;gBAAE,QAAQ;YAAI;QAC/E;QAEA,iCAAiC;QACjC,MAAM,EAAE,KAAK,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SACxC,IAAI,CAAC,kBACL,MAAM,CAAC,KAAK;YAAE,OAAO;YAAS,MAAM;QAAK,GACzC,EAAE,CAAC,WAAW,KAAK,EAAE;QAExB,IAAI,YAAY;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,UAAU,YAAY,EAAE;YACxB,YAAY;gBACV;gBACA;gBACA,OAAO,SAAS;gBAChB,YAAY,KAAK,IAAI,CAAC,CAAC,SAAS,CAAC,IAAI;YACvC;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}