#!/usr/bin/env node

/**
 * Database test script to verify all tables are working correctly
 */

const { createClient } = require('@supabase/supabase-js')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing environment variables')
  process.exit(1)
}

// Create Supabase admin client
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function testDatabase() {
  console.log('🧪 Testing database setup...\n')
  
  const tables = [
    'user_profiles',
    'photo_analyses', 
    'anonymous_uses',
    'credit_transactions'
  ]
  
  let allGood = true
  
  for (const table of tables) {
    try {
      console.log(`📋 Testing table: ${table}`)
      
      // Test basic select (should work even if empty)
      const { data, error } = await supabase
        .from(table)
        .select('*')
        .limit(1)
      
      if (error) {
        console.log(`❌ ${table}: ${error.message}`)
        allGood = false
      } else {
        console.log(`✅ ${table}: OK`)
      }
      
    } catch (err) {
      console.log(`❌ ${table}: ${err.message}`)
      allGood = false
    }
  }
  
  console.log('\n' + '='.repeat(50))
  
  if (allGood) {
    console.log('🎉 All database tables are working correctly!')
    console.log('✅ Your app is ready to use')
    console.log('\n💡 Next steps:')
    console.log('   1. Try uploading a photo')
    console.log('   2. Create a user account')
    console.log('   3. Test the age analysis feature')
  } else {
    console.log('❌ Some database issues detected')
    console.log('💡 Try running the setup again or check the logs')
  }
}

// Run the test
testDatabase().catch(console.error)
