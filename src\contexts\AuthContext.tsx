'use client'

import { createContext, useContext, useEffect, useState, useCallback } from 'react'
import { User } from '@supabase/supabase-js'
import { createSupabaseClient } from '@/lib/supabase'
import type { UserProfile } from '@/lib/supabase'

interface AuthContextType {
  user: User | null
  profile: UserProfile | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<{ error: Error | null }>
  signUp: (email: string, password: string) => Promise<{ error: Error | null }>
  signOut: () => Promise<void>
  refreshProfile: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(false) // Start with false to avoid infinite loading
  const [supabase] = useState(() => {
    try {
      return createSupabaseClient()
    } catch (error) {
      console.error('Failed to create Supabase client:', error)
      // Return a mock client to prevent crashes
      return null
    }
  })

  const fetchProfile = useCallback(async (userId: string) => {
    try {
      if (!supabase) {
        console.error('Supabase client not available for profile fetch')
        setProfile(null)
        return
      }

      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', userId)
        .single()

      if (error) {
        // If profile doesn't exist, that's okay - user just doesn't have one yet
        if (error.code === 'PGRST116') {
          console.log('User profile not found, user is anonymous')
          setProfile(null)
          return
        }
        console.error('Error fetching profile:', error)
        setProfile(null)
        return
      }

      setProfile(data)
    } catch (error) {
      console.error('Error fetching profile:', error)
      setProfile(null)
    }
  }, [supabase])

  const refreshProfile = async () => {
    if (user) {
      await fetchProfile(user.id)
    }
  }

  useEffect(() => {
    const getSession = async () => {
      try {
        console.log('AuthContext: Getting session...')

        if (!supabase) {
          console.error('Supabase client not available')
          setLoading(false)
          return
        }

        const { data: { session } } = await supabase.auth.getSession()
        console.log('AuthContext: Session received:', !!session?.user)
        setUser(session?.user ?? null)

        if (session?.user) {
          console.log('AuthContext: Fetching profile for user:', session.user.id)
          await fetchProfile(session.user.id)
        }
      } catch (error) {
        console.error('Error getting session:', error)
      } finally {
        console.log('AuthContext: Setting loading to false')
        setLoading(false)
      }
    }

    // Set a timeout to ensure loading doesn't stay true forever
    const timeoutId = setTimeout(() => {
      console.warn('Auth loading timeout reached, setting loading to false')
      setLoading(false)
    }, 5000) // 5 seconds timeout

    getSession()

    if (!supabase) {
      return () => clearTimeout(timeoutId)
    }

    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        clearTimeout(timeoutId) // Clear timeout since we got a response
        setUser(session?.user ?? null)

        if (session?.user) {
          await fetchProfile(session.user.id)
        } else {
          setProfile(null)
        }

        setLoading(false)
      }
    )

    return () => {
      subscription.unsubscribe()
      clearTimeout(timeoutId)
    }
  }, [fetchProfile, supabase.auth])

  const signIn = async (email: string, password: string) => {
    if (!supabase) {
      return { error: new Error('Supabase client not available') }
    }
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })
    return { error }
  }

  const signUp = async (email: string, password: string) => {
    if (!supabase) {
      return { error: new Error('Supabase client not available') }
    }
    const { error } = await supabase.auth.signUp({
      email,
      password,
    })
    return { error }
  }

  const signOut = async () => {
    if (!supabase) {
      return
    }
    await supabase.auth.signOut()
  }

  const value = {
    user,
    profile,
    loading,
    signIn,
    signUp,
    signOut,
    refreshProfile,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
