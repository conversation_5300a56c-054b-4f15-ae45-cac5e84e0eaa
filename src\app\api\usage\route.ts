import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase-server'
import { checkUsageLimit } from '@/lib/credits'
import { getClientIP } from '@/lib/utils'
import { initializeDatabaseIfNeeded, getDatabaseSetupInstructions } from '@/lib/database-init'

export async function GET(request: NextRequest) {
  try {
    // Check if database is initialized
    const dbReady = await initializeDatabaseIfNeeded()
    if (!dbReady) {
      return NextResponse.json(
        {
          error: 'Database setup required',
          setupInstructions: getDatabaseSetupInstructions()
        },
        { status: 503 }
      )
    }

    const supabase = await createSupabaseServerClient()
    
    // Get user if authenticated
    const { data: { user } } = await supabase.auth.getUser()
    const userId = user?.id
    const ipAddress = getClientIP(request)

    // Check usage limits
    const usageCheck = await checkUsageLimit(userId, ipAddress)
    
    // Get user profile if authenticated
    let profile = null
    if (userId) {
      const { data } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', userId)
        .single()
      profile = data
    }

    return NextResponse.json({
      success: true,
      usage: {
        canUse: usageCheck.canUse,
        remainingUses: usageCheck.remainingUses,
        isAnonymous: usageCheck.isAnonymous,
        needsCredits: usageCheck.needsCredits,
        canUseLevel: usageCheck.canUseLevel
      },
      user: user ? {
        id: user.id,
        email: user.email,
        profile: profile
      } : null
    })

  } catch (error) {
    console.error('Usage API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
